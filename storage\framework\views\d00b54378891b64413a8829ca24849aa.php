
<?php $__env->startSection('contentsite'); ?>
<?php $__env->startSection('title', 'Prioritas Part'); ?>
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="bgwhite shadow-kit p-3">
            <h5 class="h4 text-uppercase font-bold">Part Yang dikelola</h5>
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="searchName" class="form-label">Cari Nama/Kode Part</label>
                    <input type="text" id="searchName" class="form-control" placeholder="Cari nama site, nama asli, atau kode part...">
                </div>
                <div class="col-md-3">
                    <label for="searchStock" class="form-label">Cari Stock</label>
                    <input type="number" id="searchStock" class="form-control" placeholder="Jumlah stock...">
                </div>
                <div class="col-md-3">
                    <label for="partType" class="form-label">Tipe Part</label>
                    <select id="partType" class="form-control">
                        <option value="all">Semua Tipe</option>
                        <option value="AC">AC</option>
                        <option value="TYRE">TYRE</option>
                        <option value="FABRIKASI">FABRIKASI</option>
                        <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                        <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button id="resetFilters" class="btn btn-secondary w-100">Reset</button>
                </div>
            </div>
            <div id="partTable">
                <table class="table" id="partTable">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Part Code</th>
                            <th class="p-2">Part Name</th>
                            <th class="p-2">Bin Location</th>
                            <th class="p-2">Stock Saat Ini</th>
                            <th class="p-2">Min Stock</th>
                            <th class="p-2">Max Stock</th>
                            <th class="p-2">Harga</th>
                            <th class="p-2">Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="part-pagination" class="mt-3">
                </div>
            </div>
        </div>
    </div>
    <div class="col max500">
        <div class="shadow-kit bgwhite p-3">
            <h5 class="h5 text-uppercase">UBAH MIN-MAX PART</h5>
            <hr class="pt-2">
            <label for="partSearch" class="form-label">Cari Part</label>
            <input type="text" id="partSearch" class="form-control" placeholder="Cari part...">
            <div id="suggestions" class="suggestions-dropdown mt-2" style="display: none;"></div>
            <select style="display: none;" id="siteFilter" class="form-select mb-3">
                <option value="<?php echo e(session('site_id')); ?>" selected><?php echo e(session('site_name')); ?></option>
            </select>
            <form id="addPartForm" class="mt-3">
                <?php echo csrf_field(); ?>
                <div class="mb-3">
                    <label for="selectedPartName" class="form-label">Part yang Dipilih</label>
                    <input type="text" id="selectedPartName" class="form-control" readonly>
                    <input type="hidden" id="selectedPartCode">
                </div>

                <div id="siteCheckboxes">
                    <div class="mb-2">
                        <div id="siteInputs" class="mt-1">
                            <label for="sitePartName" class="form-label">Nama Part Site (Opsional):</label>
                            <input type="text" id="sitePartName" placeholder="Nama part khusus untuk site ini" class="form-control mb-1">
                            <label for="minStock" class="form-label">Min Stock:</label>
                            <input type="number" id="minStock" placeholder="Min Stock" class="form-control mb-1" value="0">
                            <label for="maxStock" class="form-label">Max Stock:</label>
                            <input type="number" id="maxStock" placeholder="Max Stock" class="form-control mb-1" value="0">
                            <label for="price" class="form-label">Harga:</label>
                            <input type="text" id="price_display" placeholder="Harga" class="form-control" value="Rp 0">
                            <input type="hidden" id="price" value="0">
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Simpan</button>
            </form>
        </div>
        <div class="col p-0">
            <div class="card shadow-kit bgwhite p-4 mt-2">
                <h3>Aktifitas Terakhir</h3>
                <hr class="mt-2">
                <ul id="activityList">
                    <!-- Data aktivitas akan dimuat di sini oleh JavaScript -->
                </ul>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')('resources/js/site/Partprioritassite.js'); ?>
<script>
    window.partPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sites/partprioritassite.blade.php ENDPATH**/ ?>