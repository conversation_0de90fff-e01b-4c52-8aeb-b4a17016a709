import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
let unitsCurrentPage = 1;
let isEditMode = false;

// Store selected transaction IDs
let selectedTransactions = new Set();

// Initialize modals
let transactionModal;
let detailsModal;
let bappModal;
let attachmentPreviewModal;

// Global functions

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

// Function to get status badge class
function getStatusBadgeClass(status) {
    switch (status) {
        case 'On Process': return 'badge-primary';
        case 'MR': return 'badge-info';
        case 'Pending': return 'badge-warning text-black';
        case 'Ready WO': return 'badge-success';
        case 'Ready PO': return 'badge-success';
        case 'Selesai': return 'badge-dark';
        case 'Perbaikan': return 'badge-danger';
        default: return 'badge-secondary';
    }
}

// Function to format date in Indonesian format (1 Maret 2025)
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);

    // Array of Indonesian month names
    const monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const day = date.getDate();
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

// Helper function to format date for input fields without timezone conversion
function formatDateForInput(dateString) {
    if (!dateString) return '';

    // If it's already in YYYY-MM-DD format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return dateString;
    }

    // Handle different date formats from server
    let cleanDateString = dateString;

    if (dateString.includes('T')) {
        // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
        cleanDateString = dateString.split('T')[0];
    } else if (dateString.includes(' ')) {
        // MySQL format: YYYY-MM-DD HH:MM:SS
        cleanDateString = dateString.split(' ')[0];
    }

    // Additional safety check: if the date string contains timezone info (but not just date separators),
    // create a date object and extract the local date components manually
    if (dateString.includes('Z') || (dateString.includes('+') && dateString.indexOf('+') > 10) ||
        (dateString.includes('-') && dateString.lastIndexOf('-') > 7)) {
        try {
            // Parse the date and extract local date components to avoid timezone conversion
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        } catch (e) {
            console.warn('Error parsing date:', dateString, e);
            return cleanDateString;
        }
    }

    return cleanDateString; // Return YYYY-MM-DD format
}

// Helper function to format datetime for datetime-local input fields without timezone conversion
function formatDateTimeForInput(dateTimeString) {
    if (!dateTimeString) return '';

    let cleanDateTimeString = dateTimeString;

    // Handle different date formats from server
    if (dateTimeString.includes('T')) {
        // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
        cleanDateTimeString = dateTimeString.split('.')[0]; // Remove milliseconds and timezone
    } else if (dateTimeString.includes(' ')) {
        // MySQL format: YYYY-MM-DD HH:MM:SS
        cleanDateTimeString = dateTimeString.replace(' ', 'T'); // Convert to ISO format
    }

    // Ensure we only take the first 16 characters (YYYY-MM-DDTHH:MM)
    return cleanDateTimeString.slice(0, 16);
}

// Function to format number as currency
function formatNumber(number) {
    return parseFloat(number).toLocaleString('id-ID');
}

// Function to format price as Indonesian Rupiah
function formatRupiah(angka) {
    if (!angka) return 'Rp 0';

    // Convert to number if it's a string
    let number = typeof angka === 'string' ? parseFloat(angka.replace(/[^\d,-]/g, '').replace(/\./g, '').replace(/,/g, '.')) : angka;

    // Format with thousand separators
    let formatted = 'Rp ' + number.toLocaleString('id-ID');

    return formatted;
}

// Function to parse Rupiah formatted string back to number
function parseRupiah(rupiahString) {
    if (!rupiahString) return 0;

    // Remove currency symbol and thousand separators
    return parseFloat(rupiahString.replace(/[^\d,-]/g, '').replace(/\./g, '').replace(/,/g, '.')) || 0;
}

// Function to show alert
function showAlert(message, type = 'success') {
    let icon = 'success';
    if (type === 'error') icon = 'error';

    Swal.fire({
        icon: icon,
        title: type === 'success' ? 'Berhasil' : 'Kesalahan',
        text: message,
        timer: 3000,
        showConfirmButton: false
    });
}

// Function to render transactions table
window.renderTransactions = function(data) {
    const tbody = document.getElementById('transactions-table-body');
    if (!tbody) return;

    tbody.innerHTML = '';

    // Update the selected count display
    updateSelectedCount();

    // Get the current site ID
    const currentSiteId = document.getElementById('current-site-id')?.value;
    const isPPASite = currentSiteId === 'PPA';
    const isDHSite = currentSiteId === 'DH';

    if (!data.data || data.data.length === 0) {
        // Determine the number of columns based on site
        let colSpan = 8; // Default for most sites
        if (isPPASite) colSpan = 10; // PPA has more columns
        if (isDHSite) colSpan = 9; // DH has 9 columns (including No. IREQ and BAPP Number)

        tbody.innerHTML = `<tr><td colspan="${colSpan}" class="text-center">Tidak ada transaksi ditemukan</td></tr>`;
        const paginationContainer = document.getElementById('pagination-container');
        if (paginationContainer) {
            paginationContainer.innerHTML = '';
        }
        return;
    }

    let i = 1;
    data.data.forEach(transaction => {
        const isChecked = selectedTransactions.has(transaction.id.toString()) ? 'checked' : '';

        // For DH site, use a different row structure with only the required columns
        if (isDHSite) {
            const row = `
                <tr>
                    <td>
                        <div class="form-check">
                            <input class="form-check-input transaction-checkbox" type="checkbox"
                                value="${transaction.id}" id="checkbox-${transaction.id}" ${isChecked}>
                        </div>
                    </td>
                    <td>${i++}</td>
                    <td>${transaction.unit.unit_code} - ${transaction.unit.unit_type}</td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(transaction.status)}">
                            ${transaction.status}
                        </span>
                        ${transaction.attachment_path ?
                        `<a href="javascript:void(0);" onclick="showAttachmentPreview('/assets/lampiranunits/${transaction.attachment_path}', '${transaction.attachment_path}')" class="badge badge-sm bg-info text-white ms-1" style="font-size: 10px; cursor: pointer;" title="Lihat Lampiran">
                            <i class="fas fa-paperclip"></i> Lampiran
                        </a>` : ''}
                    </td>
                    <td>${transaction.noireq || '-'}</td> <!-- No. IREQ column -->
                    <td>${transaction.mr_date ? formatDate(transaction.mr_date) : '-'}</td>
                    <td>${transaction.po_number || '-'}</td>
                    <td>${transaction.do_number || '-'}</td> <!-- BAPP Number column -->
                    <td>
                        <button class="btn btn-sm btn-info btn-view-transaction" data-id="${transaction.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${(transaction.status !== 'Ready PO' && transaction.status !== 'selesai') ?
                            `<button class="btn btn-sm btn-primary ml-1 btn-edit-transaction" data-id="${transaction.id}">
                                <i class="fas fa-edit"></i>
                            </button>` : ''}
                    </td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', row);
        } else {
            // Standard row structure for other sites
            const row = `
                <tr>
                    <td>
                        <div class="form-check">
                            <input class="form-check-input transaction-checkbox" type="checkbox"
                                value="${transaction.id}" id="checkbox-${transaction.id}" ${isChecked}>
                        </div>
                    </td>
                    <td>${i++}</td>
                    <td>${transaction.unit.unit_code} - ${transaction.unit.unit_type}</td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(transaction.status)}">
                            ${transaction.status}
                        </span>
                        ${transaction.attachment_path ?
                        `<a href="javascript:void(0);" onclick="showAttachmentPreview('/assets/lampiranunits/${transaction.attachment_path}', '${transaction.attachment_path}')" class="badge badge-sm bg-info text-white ms-1" style="font-size: 10px; cursor: pointer;" title="Lihat Lampiran">
                            <i class="fas fa-paperclip"></i> Lampiran
                        </a>` : ''}
                    </td>
                    <td>${transaction.wo_number || '-'}</td>
                    <td>${transaction.po_number || '-'}</td>
                    ${currentSiteId === 'IMK' ? `<td>${transaction.issue_nomor || '-'}</td>` : ''}
                    <td>${currentSiteId === 'IMK' ? (transaction.parts && transaction.parts.length > 0 ? transaction.parts[0].part_inventory?.item_code || '-' : '-') : transaction.do_number || '-'}</td>
                    ${isPPASite ? `
                    <td>${transaction.mr_date ? formatDate(transaction.mr_date) : '-'}</td>
                    <td>${transaction.do_date ? formatDate(transaction.do_date) : '-'}</td>
                    ` : ''}
                    <td>${transaction.remarks || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-info btn-view-transaction" data-id="${transaction.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${(transaction.status !== 'Ready PO' && transaction.status !== 'selesai') ?
                            `<button class="btn btn-sm btn-primary ml-1 btn-edit-transaction" data-id="${transaction.id}">
                                <i class="fas fa-edit"></i>
                            </button>` : ''}
                    </td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', row);
        }
    });

    // Add event listeners to checkboxes
    document.querySelectorAll('.transaction-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const transactionId = this.value;
            if (this.checked) {
                selectedTransactions.add(transactionId);
            } else {
                selectedTransactions.delete(transactionId);
            }
            updateSelectedCount();
        });
    });

    // Ensure pagination is rendered properly
    if (data && (data.last_page > 1 || data.total > 0)) {
        console.log('Calling renderPagination with data:', {
            current_page: data.current_page,
            last_page: data.last_page,
            total: data.total
        });
        window.renderPagination(data);
    } else {
        console.log('Not rendering pagination - insufficient data:', data);
        const paginationContainer = document.getElementById('pagination-container');
        if (paginationContainer) {
            paginationContainer.innerHTML = '';
            paginationContainer.style.display = 'none';
        }
    }
};

// Function to update the selected count display
function updateSelectedCount() {
    const countElement = document.getElementById('selected-count');
    if (countElement) {
        countElement.textContent = selectedTransactions.size;
    }

    // Enable/disable export selected dropdown button
    const exportSelectedDropdown = document.getElementById('exportSelectedDropdown');
    if (exportSelectedDropdown) {
        exportSelectedDropdown.disabled = selectedTransactions.size === 0;
    }

    // Enable/disable CETAK BAPP button for DH site
    const cetakBappBtn = document.getElementById('cetak-bapp-btn');
    if (cetakBappBtn) {
        cetakBappBtn.disabled = selectedTransactions.size === 0;
    }
}

// Function to render pagination
window.renderPagination = function(data) {
    const container = document.getElementById('pagination-container');
    if (!container) {
        console.error('Pagination container not found');
        return;
    }

    // Clear the container
    container.innerHTML = '';

    // Make sure the container is visible
    container.style.display = 'flex';
    container.style.justifyContent = 'center';
    container.style.marginTop = '1rem';

    // Check if we have pagination data
    if (!data || !data.last_page) {
        console.error('Invalid pagination data:', data);
        return;
    }

    // If there's only one page, don't show pagination
    if (data.last_page <= 1) {
        console.log('Only one page, not showing pagination');
        return;
    }

    console.log('Rendering pagination with data:', {
        current_page: data.current_page,
        last_page: data.last_page,
        total: data.total
    });

    let pagination = '<ul class="pagination pagination-sm flex-wrap">';

    // Previous button
    pagination += `
        <li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${data.current_page - 1}">&laquo;</a>
        </li>
    `;

    // Page numbers
    for (let i = 1; i <= data.last_page; i++) {
        pagination += `
            <li class="page-item ${data.current_page === i ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }

    // Next button
    pagination += `
        <li class="page-item ${data.current_page === data.last_page ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${data.current_page + 1}">&raquo;</a>
        </li>
    `;

    pagination += '</ul>';
    container.innerHTML = pagination;

    // Add click event to pagination links
    container.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            window.loadTransactions(page);
        });
    });
};

// Global function for loading units
window.loadUnits = function(page = 1) {
    unitsCurrentPage = page;
    const searchTerm = document.getElementById('unit-search')?.value || '';

    // Show loading indicator
    const tableBody = document.getElementById('units-table-body');
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat...</td></tr>';
    }

    // Create URL with parameters
    let url = `/unit-transactions/units?page=${page}`;
    if (searchTerm) url += `&search=${encodeURIComponent(searchTerm)}`;

    // Add cache-busting parameter
    url += `&_=${new Date().getTime()}`;

    // Fetch data
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            renderUnits(data);
            // Update the search input with the current search term
            // This ensures the search term is preserved when navigating between pages
            const searchInput = document.getElementById('unit-search');
            if (searchInput && searchTerm) {
                searchInput.value = searchTerm;
            }
        })
        .catch(error => {
            console.error('Error loading units:', error);
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Kesalahan memuat data</td></tr>';
            }
            showAlert('Kesalahan memuat unit: ' + error.message, 'error');
        });
};

// Function to render units table
window.renderUnits = function(data) {
    const tbody = document.getElementById('units-table-body');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data.data || data.data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Tidak ada unit ditemukan</td></tr>';
        const paginationContainer = document.getElementById('units-pagination-container');
        if (paginationContainer) {
            paginationContainer.innerHTML = '';
        }
        return;
    }

    data.data.forEach(unit => {
        const row = `
            <tr>
                <td>${unit.unit_code}</td>
                <td>${unit.unit_type}</td>
                <td>${unit.site_id}</td>
                <td>
                    <button class="btn btn-sm btn-primary btn-add-transaction" data-unit-id="${unit.id}">
                        <i class="fas fa-plus"></i> Add Out Stock
                    </button>
                </td>
            </tr>
        `;
        tbody.insertAdjacentHTML('beforeend', row);
    });

    // Update pagination
    renderUnitsPagination(data);

    // Update entries info
    const entriesInfo = document.querySelector('#units-table').closest('.card-body').querySelector('.d-flex > div > span');
    if (entriesInfo) {
        const start = (data.current_page - 1) * data.per_page + 1;
        const end = Math.min(data.current_page * data.per_page, data.total);
        entriesInfo.textContent = `Showing ${start} to ${end} of ${data.total} entries`;
    }

    // Update unit filter dropdown
    updateUnitFilterDropdown(data.data);
};

// Function to update unit filter dropdown
function updateUnitFilterDropdown(units) {
    const unitFilter = document.getElementById('unit-filter');
    if (!unitFilter) return;

    // Keep the current selection
    const currentSelection = unitFilter.value;

    // Get existing options (keep the 'All Units' option)
    const allUnitsOption = unitFilter.querySelector('option[value=""]');

    // Clear existing options
    unitFilter.innerHTML = '';

    // Add back the 'All Units' option
    if (allUnitsOption) {
        unitFilter.appendChild(allUnitsOption);
    } else {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'All Units';
        unitFilter.appendChild(option);
    }

    // Add unit options
    units.forEach(unit => {
        const option = document.createElement('option');
        option.value = unit.id;
        option.textContent = `${unit.unit_code} - ${unit.unit_type}`;
        unitFilter.appendChild(option);
    });

    // Restore selection if possible
    if (currentSelection) {
        unitFilter.value = currentSelection;
    }
}

// Function to render units pagination
window.renderUnitsPagination = function(data) {
    const container = document.getElementById('units-pagination-container');
    if (!container) return;

    container.innerHTML = '';

    if (data.last_page <= 1) return;

    let pagination = '<ul class="pagination pagination-sm flex-wrap">';

    // Previous button
    pagination += `
        <li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${data.current_page - 1}">&laquo;</a>
        </li>
    `;

    // Page numbers
    for (let i = 1; i <= data.last_page; i++) {
        pagination += `
            <li class="page-item ${data.current_page === i ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }

    // Next button
    pagination += `
        <li class="page-item ${data.current_page === data.last_page ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${data.current_page + 1}">&raquo;</a>
        </li>
    `;

    pagination += '</ul>';
    container.innerHTML = pagination;

    // Add click event to pagination links
    container.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            // Pass the current page to loadUnits
            // The search term will be retrieved from the input field
            window.loadUnits(page);
        });
    });
};

// Global function for loading transactions
window.loadTransactions = function(page = 1) {
    currentPage = page;
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const unitFilter = document.getElementById('unit-filter')?.value || '';
    const searchTerm = document.getElementById('transaction-search')?.value || '';
    const dateFrom = document.getElementById('date-from')?.value || '';
    const dateTo = document.getElementById('date-to')?.value || '';
    const currentSiteId = document.getElementById('current-site-id')?.value || '';

    // Show loading indicator
    const tableBody = document.getElementById('transactions-table-body');
    if (tableBody) {
        // Determine the number of columns based on site
        let colSpan = 9; // Default for most sites
        if (currentSiteId === 'PPA') colSpan = 11; // PPA has more columns
        if (currentSiteId === 'DH') colSpan = 9; // DH has 9 columns (including No. IREQ and BAPP Number)

        tableBody.innerHTML = `<tr><td colspan="${colSpan}" class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat...</td></tr>`;
    }

    // Create URL with parameters
    let url = `/unit-transactions/data?page=${page}`;

    // For DH site, filter to only show specific statuses if no status filter is already applied
    if (currentSiteId === 'DH' && !statusFilter) {
        url += `&status=on%20process,MR,Ready%20PO`;
    } else if (statusFilter) {
        url += `&status=${statusFilter}`;
    }

    if (unitFilter) url += `&unit_id=${unitFilter}`;
    if (searchTerm) url += `&search=${encodeURIComponent(searchTerm)}`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;

    // Add cache-busting parameter
    url += `&_=${new Date().getTime()}`;

    // Fetch data
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Transaction data loaded:', {
                current_page: data.current_page,
                last_page: data.last_page,
                total: data.total,
                per_page: data.per_page,
                data_length: data.data ? data.data.length : 0
            });

            // Render the transactions table
            window.renderTransactions(data);

            // Update the search input with the current search term
            // This ensures the search term is preserved when navigating between pages
            const searchInput = document.getElementById('transaction-search');
            if (searchInput && searchTerm) {
                searchInput.value = searchTerm;
            }

            // Update date inputs if they were set by the server
            if (data.date_from && document.getElementById('date-from')) {
                document.getElementById('date-from').value = data.date_from;
            }
            if (data.date_to && document.getElementById('date-to')) {
                document.getElementById('date-to').value = data.date_to;
            }
        })
        .catch(error => {
            console.error('Error loading transactions:', error);
            if (tableBody) {
                // Use the same colSpan as defined earlier
                const currentSiteId = document.getElementById('current-site-id')?.value || '';
                let colSpan = 9; // Default for most sites
                if (currentSiteId === 'PPA') colSpan = 11; // PPA has more columns
                if (currentSiteId === 'DH') colSpan = 9; // DH has 9 columns (including No. IREQ and BAPP Number)

                tableBody.innerHTML = `<tr><td colspan="${colSpan}" class="text-center text-danger">Kesalahan memuat data</td></tr>`;
            }
            showAlert('Kesalahan memuat transaksi: ' + error.message, 'error');
        });
};

// Function to close transaction modal
function closeTransactionModal() {
    const modal = document.getElementById('transactionModal');
    modal.classList.remove('show');
    modal.style.display = 'none';
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Remove backdrop
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
}

// Function to close details modal
function closeDetailsModal() {
    const modal = document.getElementById('transactionDetailsModal');
    modal.classList.remove('show');
    modal.style.display = 'none';
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Remove backdrop
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
}

// Function to close BAPP modal
function closeBappModal() {
    const modal = document.getElementById('bappModal');
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
}

// Function to close attachment preview modal
function closeAttachmentPreviewModal() {
    const modal = document.getElementById('attachmentPreviewModal');
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
}

// Function to show attachment preview
window.showAttachmentPreview = function(attachmentUrl, fileName) {
    // Set the download button URL
    const downloadBtn = document.getElementById('download-attachment-btn');
    if (downloadBtn) {
        downloadBtn.href = attachmentUrl;
        downloadBtn.setAttribute('download', fileName);
    }

    // Get file extension
    const fileExtension = fileName.split('.').pop().toLowerCase();

    // Show loading spinner
    const previewContainer = document.getElementById('attachment-preview-container');
    const loadingSpinner = document.getElementById('attachment-preview-content').querySelector('.text-center');

    if (previewContainer) {
        previewContainer.classList.add('d-none');
    }

    if (loadingSpinner) {
        loadingSpinner.classList.remove('d-none');
    }

    // Show the modal
    const modal = document.getElementById('attachmentPreviewModal');
    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'block';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';

        // Add backdrop if it doesn't exist
        if (!document.querySelector('.modal-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.classList.add('modal-backdrop', 'fade', 'show');
            document.body.appendChild(backdrop);
        }
    }

    // Set modal title with file name
    const modalTitle = document.getElementById('attachmentPreviewModalLabel');
    if (modalTitle) {
        modalTitle.textContent = 'Lampiran: ' + fileName;
    }

    // Determine content based on file type
    let previewContent = '';

    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
        // Image preview
        previewContent = `<img src="${attachmentUrl}" class="img-fluid" alt="Attachment Preview">`;
    } else if (fileExtension === 'pdf') {
        // PDF preview
        previewContent = `<iframe src="${attachmentUrl}" width="100%" height="600" frameborder="0"></iframe>`;
    } else {
        // For other file types, show a download message
        previewContent = `
            <div class="text-center">
                <i class="fas fa-file fa-5x mb-3"></i>
                <p>File tidak dapat ditampilkan secara langsung. Silakan unduh file untuk melihatnya.</p>
                <a href="${attachmentUrl}" class="btn btn-primary" download="${fileName}">
                    <i class="fas fa-download"></i> Unduh ${fileName}
                </a>
            </div>
        `;
    }

    // Update preview container and hide loading spinner
    if (previewContainer) {
        previewContainer.innerHTML = previewContent;
        previewContainer.classList.remove('d-none');
    }

    if (loadingSpinner) {
        loadingSpinner.classList.add('d-none');
    }
}

// Import bootstrap if needed
// This is a fallback in case window.bootstrap is not available
function initializeModals() {
    // Initialize modals
    const transactionModalEl = document.getElementById('transactionModal');
    const detailsModalEl = document.getElementById('transactionDetailsModal');
    const bappModalEl = document.getElementById('bappModal');
    const attachmentPreviewModalEl = document.getElementById('attachmentPreviewModal');

    if (!transactionModalEl || !detailsModalEl) {
        console.error('Modal elements not found');
        return;
    }

    // Setup close buttons for Bootstrap 5
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal.id === 'transactionModal') {
                closeTransactionModal();
            } else if (modal.id === 'transactionDetailsModal') {
                closeDetailsModal();
            } else if (modal.id === 'bappModal') {
                closeBappModal();
            } else if (modal.id === 'attachmentPreviewModal') {
                closeAttachmentPreviewModal();
            }
        });
    });

    // Add ESC key listener for closing modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeTransactionModal();
            closeDetailsModal();
            closeBappModal();
            closeAttachmentPreviewModal();
        }
    });

    // Check if bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        // Initialize modals
        transactionModal = new bootstrap.Modal(transactionModalEl);
        detailsModal = new bootstrap.Modal(detailsModalEl);
        if (bappModalEl) {
            bappModal = new bootstrap.Modal(bappModalEl);
        }
        if (attachmentPreviewModalEl) {
            attachmentPreviewModal = new bootstrap.Modal(attachmentPreviewModalEl);
        }
    } else {
        console.error('Bootstrap is not defined. Trying to load it from window.bootstrap');
        // Try to use window.bootstrap as a fallback
        if (window.bootstrap) {
            transactionModal = new window.bootstrap.Modal(transactionModalEl);
            detailsModal = new window.bootstrap.Modal(detailsModalEl);
            if (bappModalEl) {
                bappModal = new window.bootstrap.Modal(bappModalEl);
            }
            if (attachmentPreviewModalEl) {
                attachmentPreviewModal = new window.bootstrap.Modal(attachmentPreviewModalEl);
            }
        } else {
            console.error('Bootstrap is not available. Modal functionality will be limited.');
            // Implement a basic modal show/hide functionality as fallback
        }
    }
}

// Initialize modals when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a short time to ensure bootstrap is loaded
    setTimeout(initializeModals, 100);
});

// Function to handle status change and show/hide WO/PO fields
function handleStatusChange() {
    const status = document.getElementById('status').value;
    const woContainer = document.getElementById('wo_number_container');
    const poContainer = document.getElementById('po_number_container');
    const issueNomorContainer = document.getElementById('issue_nomor_container');
    const currentSiteId = document.getElementById('current-site-id')?.value || '';

    // For IMK site, always show the No Project and Issue Nomor fields, but hide PO Number field
    if (currentSiteId === 'IMK') {
        woContainer.style.display = 'block';
        poContainer.style.display = 'none'; // Hide PO Number field for IMK site
        if (issueNomorContainer) {
            issueNomorContainer.style.display = 'block';
        }
    }
    // For UDU site, always show the No. PR field (wo_number_container)
    else if (currentSiteId === 'UDU') {
        woContainer.style.display = 'block';

        // Only show PO field for Ready PO status
        poContainer.style.display = status === 'Ready PO' ? 'block' : 'none';
    }
    // For DH site, only show PO Number and hide WO Number
    else if (currentSiteId === 'DH') {
        woContainer.style.display = 'none';
        poContainer.style.display = 'block';

        // Hide other form fields that are not relevant for DH site
        hideNonDHFields();

        // Set up hidden field synchronization for DH site
        setupDHHiddenFields();
    } else {
        // For other sites, use the original logic
        // Hide both containers by default
        woContainer.style.display = 'none';
        poContainer.style.display = 'none';

        // Show appropriate containers based on status
        if (status === 'Ready WO') {
            woContainer.style.display = 'block';
        } else if (status === 'Ready PO') {
            // For Ready PO, show both WO and PO fields
            woContainer.style.display = 'block';
            poContainer.style.display = 'block';
        }
    }
}

// Function to set up hidden field synchronization for DH site
function setupDHHiddenFields() {
    // Synchronize visible fields with hidden fields for form submission
    const fieldsToSync = [
        { visible: 'updated_at', hidden: 'updated_at_hidden' },
        { visible: 'do_date', hidden: 'do_date_hidden' },
        { visible: 'do_number', hidden: 'do_number_hidden' },
        { visible: 'noSPB', hidden: 'noSPB_hidden' },
        { visible: 'pekerjaan', hidden: 'pekerjaan_hidden' },
        { visible: 'HMKM', hidden: 'HMKM_hidden' },
        { visible: 'SHIFT', hidden: 'SHIFT_hidden' },
        { visible: 'LOKASI', hidden: 'LOKASI_hidden' },
        { visible: 'remarks', hidden: 'remarks_hidden' }
    ];

    fieldsToSync.forEach(field => {
        const visibleField = document.getElementById(field.visible);
        const hiddenField = document.getElementById(field.hidden);

        if (visibleField && hiddenField) {
            // Initial sync
            hiddenField.value = visibleField.value;

            // Set up event listener for future changes
            visibleField.addEventListener('change', function() {
                hiddenField.value = visibleField.value;
            });
        }
    });
}

// Function to hide non-DH fields
function hideNonDHFields() {
    const currentSiteId = document.getElementById('current-site-id')?.value || '';

    if (currentSiteId === 'DH') {
        // Hide fields that are not relevant for DH site
        // Hide updated_at field
        const updatedAtField = document.getElementById('updated_at');
        if (updatedAtField) {
            updatedAtField.closest('.col-md-6').style.display = 'none';
        }

        // Hide do_date field
        const doDateField = document.getElementById('do_date');
        if (doDateField) {
            doDateField.closest('.col-md-6').style.display = 'none';
        }

        // Hide do_number field
        const doNumberField = document.getElementById('do_number');
        if (doNumberField) {
            const doNumberContainer = document.getElementById('do_number_container');
            if (doNumberContainer) {
                doNumberContainer.style.display = 'none';
            }
        }

        // Hide noSPB field
        const noSPBField = document.getElementById('noSPB');
        if (noSPBField) {
            const noSPBContainer = document.getElementById('noSPB_container');
            if (noSPBContainer) {
                noSPBContainer.style.display = 'none';
            }
        }

        // Hide entire "Informasi Pekerjaan" section
        const pekerjaanSection = document.getElementById('pekerjaan').closest('.shadow-kit');
        if (pekerjaanSection) {
            pekerjaanSection.style.display = 'none';

            // Also hide the parent col-md-4 container
            const pekerjaanCol = pekerjaanSection.closest('.col-md-4');
            if (pekerjaanCol) {
                pekerjaanCol.style.display = 'none';
            }
        }

        // Hide entire "Keterangan" section
        const remarksSection = document.getElementById('remarks').closest('.shadow-kit');
        if (remarksSection) {
            remarksSection.style.display = 'none';

            // Also hide the parent col-md-4 container
            const remarksCol = remarksSection.closest('.col-md-4');
            if (remarksCol) {
                remarksCol.style.display = 'none';
            }
        }

        // Hide contact/customer section
        const contactSection = document.getElementById('contact').closest('.shadow-kit');
        if (contactSection) {
            contactSection.style.display = 'none';

            // Also hide the parent col-md-4 container
            const contactCol = contactSection.closest('.col-md-4');
            if (contactCol) {
                contactCol.style.display = 'none';
            }
        }

        // Hide the "Dokumen & Nomor" section if all its children are hidden
        const documentSection = document.getElementById('po_number').closest('.shadow-kit');
        if (documentSection) {
            // Check if all visible elements inside are hidden
            const visibleElements = documentSection.querySelectorAll('.form-group:not([style*="display: none"])');
            if (visibleElements.length <= 1) { // Only PO number should be visible
                // Keep the section visible but adjust its width
                const documentCol = documentSection.closest('.col-md-4');
                if (documentCol) {
                    documentCol.className = 'col-md-8'; // Make it wider

                    // Move it to the center
                    const parentRow = documentCol.parentElement;
                    if (parentRow) {
                        parentRow.style.justifyContent = 'center';
                    }
                }
            }
        }

        // Reorganize the layout for DH site
        reorganizeDHLayout();
    }
}

// Function to reorganize layout for DH site
function reorganizeDHLayout() {
    const currentSiteId = document.getElementById('current-site-id')?.value || '';

    if (currentSiteId === 'DH') {
        // Find the mr_date field and make it more prominent
        const mrDateField = document.getElementById('mr_date');
        if (mrDateField) {
            const mrDateCol = mrDateField.closest('.col-md-6');
            if (mrDateCol) {
                mrDateCol.className = 'col-md-12'; // Make it full width
                mrDateCol.style.marginBottom = '15px'; // Add spacing
            }
        }

        // Find the po_number field and make it more prominent
        const poNumberField = document.getElementById('po_number');
        if (poNumberField) {
            const poNumberContainer = document.getElementById('po_number_container');
            if (poNumberContainer) {
                poNumberContainer.className = 'form-group mb-3 col-md-12'; // Make it full width
                poNumberContainer.style.marginBottom = '15px'; // Add spacing

                // Make the label more prominent
                const poLabel = poNumberContainer.querySelector('label');
                if (poLabel) {
                    poLabel.className = 'form-label fw-bold mb-2';
                    poLabel.style.fontSize = '14px';
                }

                // Make the input field larger
                const poInput = poNumberContainer.querySelector('input');
                if (poInput) {
                    poInput.className = 'form-control';
                    poInput.style.fontSize = '14px';
                    poInput.style.padding = '8px 12px';
                }
            }
        }

        // Find the noireq field and make it more prominent
        const noireqField = document.getElementById('noireq');
        if (noireqField) {
            const noireqCol = noireqField.closest('.col-md-12');
            if (noireqCol) {
                noireqCol.style.marginBottom = '15px'; // Add spacing

                // Make the label more prominent
                const noireqLabel = noireqCol.querySelector('label');
                if (noireqLabel) {
                    noireqLabel.className = 'form-label fw-bold mb-2';
                    noireqLabel.style.fontSize = '14px';
                }

                // Make the input field larger
                noireqField.className = 'form-control';
                noireqField.style.fontSize = '14px';
                noireqField.style.padding = '8px 12px';
            }
        }

        // Make sure the attachment section is always visible
        const attachmentSection = document.getElementById('attachment').closest('.col-md-4');
        if (attachmentSection) {
            // Make sure the attachment section is visible
            attachmentSection.style.display = 'block';
            // Make it full width
            attachmentSection.className = 'col-md-12';

            // Make sure the parent row is visible
            const secondRow = document.querySelector('.row.mt-2');
            if (secondRow) {
                secondRow.style.display = 'flex';
                // Center the attachment section
                secondRow.style.justifyContent = 'center';
            }
        }
    }
}

// Function to ensure attachment section is always visible
function ensureAttachmentSectionVisible() {
    // Make sure the attachment section is always visible
    const attachmentSection = document.getElementById('attachment-section');
    if (attachmentSection) {
        // Make sure the attachment section is visible
        attachmentSection.style.display = 'block';

        // For DH site, make it full width
        if (document.getElementById('current-site-id')?.value === 'DH') {
            attachmentSection.className = 'col-md-12';

            // Make sure the parent row is visible
            const secondRow = attachmentSection.closest('.row.mt-2');
            if (secondRow) {
                secondRow.style.display = 'flex';
                // Center the attachment section
                secondRow.style.justifyContent = 'center';
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dropify for file uploads
    if (typeof $ !== 'undefined' && $.fn.dropify) {
        $('.dropify').dropify({
            messages: {
                default: 'Drag and drop a file here or click',
                replace: 'Drag and drop or click to replace',
                remove: 'Remove',
                error: 'Ooops, something wrong happened.'
            },
            error: {
                fileSize: 'The file size is too big (5M max).'
            }
        });
    }

    // Ensure attachment section is visible for all sites
    ensureAttachmentSectionVisible();

    // Check if we're on DH site and hide non-relevant fields
    if (document.getElementById('current-site-id')?.value === 'DH') {
        // Apply DH site specific styling immediately
        hideNonDHFields();

        // Also apply when modal is shown (for edit mode)
        document.addEventListener('shown.bs.modal', function(event) {
            if (event.target.id === 'transactionModal') {
                hideNonDHFields();

                // Call the function to ensure attachment section is visible
                ensureAttachmentSectionVisible();
            }
        });
    }

    // Handle CETAK BAPP button click
    const cetakBappBtn = document.getElementById('cetak-bapp-btn');
    if (cetakBappBtn) {
        cetakBappBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;

            // Set the selected transaction IDs in the form
            const selectedIds = Array.from(selectedTransactions);
            document.getElementById('bapp_unit_transaction_ids').value = JSON.stringify(selectedIds);

            // Set default dates
            const today = new Date();
            const formattedToday = today.toISOString().split('T')[0];
            document.getElementById('bapp_tanggalstart').value = formattedToday;
            document.getElementById('bapp_tanggalend').value = formattedToday;

            // Show the BAPP modal
            if (bappModal) {
                bappModal.show();
            } else {
                const bappModalEl = document.getElementById('bappModal');
                if (bappModalEl) {
                    bappModal = new bootstrap.Modal(bappModalEl);
                    bappModal.show();
                }
            }
        });
    }

    // Handle BAPP preview button click
    const previewBappBtn = document.getElementById('preview-bapp-btn');
    if (previewBappBtn) {
        previewBappBtn.addEventListener('click', function() {
            // Get form data
            const formData = new FormData(document.getElementById('bappForm'));

            // Validate form
            const tanggalstart = formData.get('tanggalstart');
            const tanggalend = formData.get('tanggalend');
            const noBA = formData.get('noBA');

            if (!tanggalstart || !tanggalend || !noBA) {
                showAlert('Silakan isi semua field yang diperlukan', 'error');
                return;
            }

            // Show loading overlay
            showFileLoadingOverlay();

            // Convert unit_transaction_ids from JSON string to array
            const unitTransactionIds = JSON.parse(formData.get('unit_transaction_ids'));
            formData.delete('unit_transaction_ids');

            // Add each ID as a separate entry
            unitTransactionIds.forEach(id => {
                formData.append('unit_transaction_ids[]', id);
            });

            // Create a form to submit directly to the preview endpoint
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/unit-transactions/preview-bapp';
            form.target = '_blank';
            form.style.display = 'none';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').content;
            form.appendChild(csrfToken);

            // Add form fields
            const tanggalstartInput = document.createElement('input');
            tanggalstartInput.type = 'hidden';
            tanggalstartInput.name = 'tanggalstart';
            tanggalstartInput.value = tanggalstart;
            form.appendChild(tanggalstartInput);

            const tanggalendInput = document.createElement('input');
            tanggalendInput.type = 'hidden';
            tanggalendInput.name = 'tanggalend';
            tanggalendInput.value = tanggalend;
            form.appendChild(tanggalendInput);

            const noBAInput = document.createElement('input');
            noBAInput.type = 'hidden';
            noBAInput.name = 'noBA';
            noBAInput.value = noBA;
            form.appendChild(noBAInput);

            // Add unit transaction IDs
            unitTransactionIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'unit_transaction_ids[]';
                input.value = id;
                form.appendChild(input);
            });

            // Add form to document, submit it, and remove it
            document.body.appendChild(form);
            form.submit();

            // Hide loading overlay after a short delay
            setTimeout(() => {
                hideFileLoadingOverlay();

                // Close the modal
                if (bappModal) {
                    bappModal.hide();
                } else {
                    closeBappModal();
                }
            }, 1000);
        });
    }

    // Initialize part search autocomplete
    const partSearchInput = document.getElementById('part-search');
    const partSearchResults = document.getElementById('part-search-results');
    let selectedPartIndex = -1;
    let currentSearchResults = [];

    if (partSearchInput && partSearchResults) {
        // Add input event listener for search
        partSearchInput.addEventListener('input', debounce(function() {
            const searchTerm = partSearchInput.value.trim();
            if (searchTerm.length < 2) {
                partSearchResults.style.display = 'none';
                return; // Require at least 2 characters
            }

            // Reset selected index
            selectedPartIndex = -1;

            // Fetch parts matching the search term
            fetch(`/unit-transactions/search-parts?q=${encodeURIComponent(searchTerm)}`)
                .then(response => response.json())
                .then(parts => {
                    // Store the search results
                    currentSearchResults = parts;

                    // Display search results in dropdown
                    if (parts.length > 0) {
                        let resultsHtml = '';
                        parts.forEach((part, index) => {
                            resultsHtml += `
                                <a href="#" class="dropdown-item part-result" data-index="${index}">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>${part.part_name}</strong><br>
                                            <small>Code: ${part.part_code}</small>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-info">Stock: ${parseFloat(part.stock_quantity).toFixed(1)}</span><br>
                                            <small>${formatRupiah(part.price)}</small>
                                        </div>
                                    </div>
                                </a>
                            `;
                        });
                        partSearchResults.innerHTML = resultsHtml;
                        partSearchResults.style.display = 'block';

                        // Add click event to search results
                        document.querySelectorAll('.part-result').forEach(item => {
                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                const index = parseInt(this.dataset.index);
                                selectPart(index);
                            });
                        });
                    } else {
                        partSearchResults.innerHTML = '<span class="dropdown-item">No parts found</span>';
                        partSearchResults.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error searching parts:', error);
                    partSearchResults.innerHTML = '<span class="dropdown-item text-danger">Error searching parts</span>';
                    partSearchResults.style.display = 'block';
                });
        }, 300));

        // Handle keyboard navigation in dropdown
        partSearchInput.addEventListener('keydown', function(e) {
            if (partSearchResults.style.display === 'none') return;

            // Down arrow
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (selectedPartIndex < currentSearchResults.length - 1) {
                    selectedPartIndex++;
                    highlightSelectedPart();
                }
            }
            // Up arrow
            else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (selectedPartIndex > 0) {
                    selectedPartIndex--;
                    highlightSelectedPart();
                }
            }
            // Enter key
            else if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedPartIndex >= 0 && selectedPartIndex < currentSearchResults.length) {
                    selectPart(selectedPartIndex);
                }
            }
            // Escape key
            else if (e.key === 'Escape') {
                e.preventDefault();
                partSearchResults.style.display = 'none';
            }
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!partSearchInput.contains(e.target) && !partSearchResults.contains(e.target)) {
                partSearchResults.style.display = 'none';
            }
        });
    }

    // Function to highlight selected part in dropdown
    function highlightSelectedPart() {
        document.querySelectorAll('.part-result').forEach((item, index) => {
            if (index === selectedPartIndex) {
                item.classList.add('active');
                // Scroll to the selected item if needed
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('active');
            }
        });
    }

    // Function to select a part from search results
    function selectPart(index) {
        if (index >= 0 && index < currentSearchResults.length) {
            const selectedPart = currentSearchResults[index];

            // Check if this part is already in the transaction
            const existingPartItem = document.querySelector(`.part-item[data-part-id="${selectedPart.part_inventory_id}"]`);

            if (existingPartItem) {
                // Part already exists in the transaction
                showAlert(`Part ${selectedPart.part_name} sudah ada dalam transaksi ini`, 'error');

                // Highlight the existing part item temporarily
                existingPartItem.classList.add('bg-warning');

                // Scroll to the existing part to make it visible
                existingPartItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

                setTimeout(() => {
                    existingPartItem.classList.remove('bg-warning');
                }, 2000);

                // Clear the search input and hide dropdown
                const partSearchInput = document.getElementById('part-search');
                if (partSearchInput) {
                    partSearchInput.value = '';
                    document.getElementById('part-search-results').style.display = 'none';
                    currentSearchResults = [];
                }

                return;
            }

            // Create a part object in the format expected by addPartToForm
            const part = {
                part_inventory_id: selectedPart.part_inventory_id,
                quantity: 1, // Default quantity
                price: selectedPart.price || 0,
                eum: selectedPart.eum || 'EA',
                part: {
                    part_name: selectedPart.part_name,
                    part_code: selectedPart.part_code
                }
            };

            // Add the part to the form
            addPartToForm(part);

            // Clear the search input and hide dropdown
            const partSearchInput = document.getElementById('part-search');
            if (partSearchInput) {
                partSearchInput.value = '';
                document.getElementById('part-search-results').style.display = 'none';
                currentSearchResults = [];
            }

            showAlert(`Part ${selectedPart.part_name} berhasil ditambahkan`, 'success');
        }
    }

    // Add part button click handler
    const addPartBtn = document.getElementById('add-part-btn');
    if (addPartBtn) {
        addPartBtn.addEventListener('click', function() {
            const partSearchInput = document.getElementById('part-search');
            const searchTerm = partSearchInput.value.trim();

            if (searchTerm.length < 2) {
                showAlert('Silakan masukkan nama part untuk dicari', 'error');
                return;
            }

            if (currentSearchResults.length === 0) {
                showAlert('Tidak ada part yang ditemukan. Silakan cari dengan kata kunci lain.', 'error');
                return;
            }

            // Use the first result if no part is selected
            selectPart(selectedPartIndex >= 0 ? selectedPartIndex : 0);
        });
    }
    // Get the current site ID from the hidden input field
    const siteId = document.getElementById('current-site-id')?.value || '';

    console.log('Current Site ID:', siteId);

    // Show/hide report options based on site ID
    if (siteId === 'IMK') {
        // For IMK site: Hide WO report options, show only PO report options
        const woReportElements = [
            document.getElementById('preview-outgoing'),
            document.getElementById('download-outgoing'),
            document.getElementById('preview-selected-outgoing'),
            document.getElementById('download-selected-outgoing')
        ];

        woReportElements.forEach(element => {
            if (element) {
                element.style.display = 'none';
            }
        });
    } else if (siteId === 'DH') {
        // For DH site: Hide all options except BAPP and Slip Store
        // This is a backup in case the Blade template conditions don't work
        const hideElements = [
            document.getElementById('preview-outgoing'),
            document.getElementById('download-outgoing'),
            document.getElementById('preview-selected-outgoing'),
            document.getElementById('download-selected-outgoing'),
            document.getElementById('preview-report'),
            document.getElementById('download-report'),
            document.getElementById('preview-selected-report'),
            document.getElementById('download-selected-report'),
            document.getElementById('preview-slip'),
            document.getElementById('download-slip'),
            document.getElementById('preview-selected-slip'),
            document.getElementById('download-selected-slip')
        ];

        hideElements.forEach(element => {
            if (element) {
                element.style.display = 'none';
            }
        });
    } else if (siteId === 'PPA' || siteId === 'UDU') {
        // For PPA and UDU sites: Show both WO and PO report options (default behavior)
        // No changes needed as both options are visible by default
    } else {
        // For any other site or if site ID is not found: Show both options (default behavior)
        console.log('Unknown site ID or site ID not found, showing all report options');
    }

    // Initialize dropdown submenu functionality for Bootstrap 5
    document.querySelectorAll('.dropdown-submenu > a.dropdown-toggle').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Close all other open submenus
            document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(submenu) {
                if (submenu !== e.target.nextElementSibling) {
                    submenu.classList.remove('show');
                    submenu.style.display = 'none';
                }
            });

            // Toggle this submenu
            const submenu = e.target.nextElementSibling;
            if (submenu.classList.contains('show') || submenu.style.display === 'block') {
                submenu.classList.remove('show');
                submenu.style.display = 'none';
            } else {
                submenu.classList.add('show');
                submenu.style.display = 'block';
            }
        });
    });

    // Add event listener for status change
    const statusSelect = document.getElementById('status');
    if (statusSelect) {
        statusSelect.addEventListener('change', handleStatusChange);
        // Call once on page load to set initial state
        handleStatusChange();
    }

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(submenu) {
                submenu.classList.remove('show');
                submenu.style.display = 'none';
            });
        }
    });
    // Set default date range (yesterday to tomorrow)
    const dateFrom = document.getElementById('date-from');
    const dateTo = document.getElementById('date-to');

    if (dateFrom && dateTo) {
        // Set yesterday as default from date
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        // Use local date formatting to avoid timezone issues
        const yesterdayFormatted = yesterday.getFullYear() + '-' +
            String(yesterday.getMonth() + 1).padStart(2, '0') + '-' +
            String(yesterday.getDate()).padStart(2, '0');
        dateFrom.value = yesterdayFormatted;

        // Set tomorrow as default to date
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        // Use local date formatting to avoid timezone issues
        const tomorrowFormatted = tomorrow.getFullYear() + '-' +
            String(tomorrow.getMonth() + 1).padStart(2, '0') + '-' +
            String(tomorrow.getDate()).padStart(2, '0');
        dateTo.value = tomorrowFormatted;
    }

    // Call loadTransactions and loadUnits after a short delay to ensure DOM is fully loaded
    setTimeout(() => {
        window.loadTransactions();
        window.loadUnits();
    }, 100);

    // Add event listener for unit search
    const unitSearchInput = document.getElementById('unit-search');
    if (unitSearchInput) {
        unitSearchInput.addEventListener('input', debounce(function() {
            // Always reset to page 1 when searching
            window.loadUnits(1);
        }, 300));
    }

    // Add event listener for transaction search
    const transactionSearchInput = document.getElementById('transaction-search');
    if (transactionSearchInput) {
        transactionSearchInput.addEventListener('input', debounce(function() {
            // Always reset to page 1 when searching
            window.loadTransactions(1);
        }, 300));
    }

    // Handle filter changes
    document.getElementById('status-filter').addEventListener('change', function() {
        window.loadTransactions(1);
    });

    document.getElementById('unit-filter').addEventListener('change', function() {
        window.loadTransactions(1);
    });

    // Handle date range filter changes
    document.getElementById('date-from').addEventListener('change', function() {
        window.loadTransactions(1);
    });

    document.getElementById('date-to').addEventListener('change', function() {
        window.loadTransactions(1);
    });

    // Function to get current filter parameters
    function getFilterParams() {
        const statusFilter = document.getElementById('status-filter')?.value || '';
        const unitFilter = document.getElementById('unit-filter')?.value || '';
        const searchTerm = document.getElementById('transaction-search')?.value || '';
        const dateFrom = document.getElementById('date-from')?.value || '';
        const dateTo = document.getElementById('date-to')?.value || '';

        const params = new URLSearchParams();

        if (statusFilter) params.append('status', statusFilter);
        if (unitFilter) params.append('unit_id', unitFilter);
        if (searchTerm) params.append('search', searchTerm);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);

        return params;
    }

    // Function to show loading overlay
    function showFileLoadingOverlay() {
        const overlay = document.getElementById('file-loading-overlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }

    // Function to hide loading overlay
    function hideFileLoadingOverlay() {
        const overlay = document.getElementById('file-loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    // Function to handle PDF generation with template parameter
    function handlePdfGeneration(url, template, params = null) {
        // Show loading overlay
        showFileLoadingOverlay();

        // Create URL parameters if not provided
        if (!params) {
            params = getFilterParams();
        }

        // Add template parameter
        if (template) {
            params.append('template', template);
        }

        // Check if this is a selected transactions operation for PPA site
        const isPPASite = document.getElementById('current-site-id')?.value === 'PPA';
        const isSelectedOperation = url.includes('selected');
        const isPdfOperation = !url.includes('excel'); // Check if this is a PDF operation (not Excel)
        const hasMultipleTransactions = selectedTransactions.size > 1;

        // For PPA site with multiple selected transactions for PDF operations only, handle each transaction separately
        if (isPPASite && isSelectedOperation && isPdfOperation && hasMultipleTransactions) {
            // Get all selected transaction IDs
            const selectedIds = Array.from(selectedTransactions);

            // For each transaction, open a separate tab/window
            selectedIds.forEach(transactionId => {
                // Find the transaction data to get the DO number
                const transactionRow = document.querySelector(`.transaction-checkbox[value="${transactionId}"]`).closest('tr');
                let doNumber = '';

                // Get the DO number from the row (it's in the 7th column, index 6)
                if (transactionRow) {
                    const doNumberCell = transactionRow.cells[6]; // DO number column
                    if (doNumberCell) {
                        doNumber = doNumberCell.textContent.trim();
                    }
                }

                const singleParams = new URLSearchParams();

                // Add the single transaction ID
                singleParams.append('ids', transactionId);

                // Add template parameter if provided
                if (template) {
                    singleParams.append('template', template);
                }

                // Add DO number for file naming
                if (doNumber) {
                    singleParams.append('do_number', doNumber);
                }

                // Build URL with parameters
                let singleUrl = url;
                if (singleParams.toString()) {
                    singleUrl += '?' + singleParams.toString();
                }

                // Open in a new tab
                window.open(singleUrl, '_blank');
            });

            // Hide loading overlay after a short delay
            setTimeout(() => {
                hideFileLoadingOverlay();
            }, 1000);
        } else {
            // Standard behavior for non-PPA sites or single transaction
            // Build URL with parameters
            if (params.toString()) {
                url += '?' + params.toString();
            }

            // Open the URL in a new tab and hide loading overlay when window is opened
            const newWindow = window.open(url, '_blank');

            // Hide loading overlay after a short delay
            // This ensures the overlay is shown long enough for the user to see it
            setTimeout(() => {
                hideFileLoadingOverlay();
            }, 1000);

            // If window was blocked, hide the overlay immediately
            if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                hideFileLoadingOverlay();
            }
        }
    }

    // Function to check if transactions are selected
    function checkSelectedTransactions() {
        if (selectedTransactions.size === 0) {
            showAlert('Tidak ada transaksi yang dipilih', 'warning');
            return false;
        }
        return true;
    }

    // Function to get selected transaction IDs as URL parameters
    function getSelectedTransactionParams() {
        const params = new URLSearchParams();
        const selectedIds = Array.from(selectedTransactions).join(',');
        params.append('ids', selectedIds);
        return params;
    }

    // Preview Outgoing Items
    const previewOutgoingBtn = document.getElementById('preview-outgoing');
    if (previewOutgoingBtn) {
        previewOutgoingBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/preview-pdf', null);
        });
    }

    // Preview Report Document
    const previewReportBtn = document.getElementById('preview-report');
    if (previewReportBtn) {
        previewReportBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/preview-pdf', 'report');
        });
    }

    // Preview SLIP STORE
    const previewSlipBtn = document.getElementById('preview-slip');
    if (previewSlipBtn) {
        previewSlipBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/preview-pdf', 'slip');
        });
    }

    // Download Outgoing Items
    const downloadOutgoingBtn = document.getElementById('download-outgoing');
    if (downloadOutgoingBtn) {
        downloadOutgoingBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/export-pdf', null);
        });
    }

    // Download Report Document
    const downloadReportBtn = document.getElementById('download-report');
    if (downloadReportBtn) {
        downloadReportBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/export-pdf', 'report');
        });
    }

    // Download SLIP STORE
    const downloadSlipBtn = document.getElementById('download-slip');
    if (downloadSlipBtn) {
        downloadSlipBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/export-pdf', 'slip');
        });
    }

    // Preview Selected Outgoing Items
    const previewSelectedOutgoingBtn = document.getElementById('preview-selected-outgoing');
    if (previewSelectedOutgoingBtn) {
        previewSelectedOutgoingBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/preview-selected', null, getSelectedTransactionParams());
        });
    }

    // Preview Selected Report Document
    const previewSelectedReportBtn = document.getElementById('preview-selected-report');
    if (previewSelectedReportBtn) {
        previewSelectedReportBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/preview-selected', 'report', getSelectedTransactionParams());
        });
    }

    // Preview Selected SLIP STORE
    const previewSelectedSlipBtn = document.getElementById('preview-selected-slip');
    if (previewSelectedSlipBtn) {
        previewSelectedSlipBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/preview-selected', 'slip', getSelectedTransactionParams());
        });
    }

    // Preview Selected BAPP for DH site
    const previewSelectedBappBtn = document.getElementById('preview-selected-bapp');
    if (previewSelectedBappBtn) {
        previewSelectedBappBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/preview-selected', 'report', getSelectedTransactionParams());
        });
    }

    // Preview Selected Slip Store for DH site
    const previewSelectedSlipStoreBtn = document.getElementById('preview-selected-slip-store');
    if (previewSelectedSlipStoreBtn) {
        previewSelectedSlipStoreBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/preview-selected', 'slip', getSelectedTransactionParams());
        });
    }

    // Download Selected Outgoing Items
    const downloadSelectedOutgoingBtn = document.getElementById('download-selected-outgoing');
    if (downloadSelectedOutgoingBtn) {
        downloadSelectedOutgoingBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected', null, getSelectedTransactionParams());
        });
    }

    // Download Selected Report Document
    const downloadSelectedReportBtn = document.getElementById('download-selected-report');
    if (downloadSelectedReportBtn) {
        downloadSelectedReportBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected', 'report', getSelectedTransactionParams());
        });
    }

    // Download Selected SLIP STORE
    const downloadSelectedSlipBtn = document.getElementById('download-selected-slip');
    if (downloadSelectedSlipBtn) {
        downloadSelectedSlipBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected', 'slip', getSelectedTransactionParams());
        });
    }

    // Download Selected BAPP for DH site
    const downloadSelectedBappBtn = document.getElementById('download-selected-bapp');
    if (downloadSelectedBappBtn) {
        downloadSelectedBappBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected', 'report', getSelectedTransactionParams());
        });
    }

    // Download Selected Slip Store for DH site
    const downloadSelectedSlipStoreBtn = document.getElementById('download-selected-slip-store');
    if (downloadSelectedSlipStoreBtn) {
        downloadSelectedSlipStoreBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected', 'slip', getSelectedTransactionParams());
        });
    }

    // Download Excel
    const downloadExcelBtn = document.getElementById('download-excel');
    if (downloadExcelBtn) {
        downloadExcelBtn.addEventListener('click', function() {
            handlePdfGeneration('/unit-transactions/export-excel', null);
        });
    }

    // Download Selected Excel
    const downloadSelectedExcelBtn = document.getElementById('download-selected-excel');
    if (downloadSelectedExcelBtn) {
        downloadSelectedExcelBtn.addEventListener('click', function() {
            if (!checkSelectedTransactions()) return;
            handlePdfGeneration('/unit-transactions/export-selected-excel', null, getSelectedTransactionParams());
        });
    }

    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.transaction-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                const transactionId = checkbox.value;
                if (this.checked) {
                    selectedTransactions.add(transactionId);
                } else {
                    selectedTransactions.delete(transactionId);
                }
            });
            updateSelectedCount();
        });
    }

    // Handle add transaction button click (use event delegation for dynamically loaded buttons)
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-add-transaction')) {
            const button = e.target.closest('.btn-add-transaction');
            resetForm();
            isEditMode = false;

            const unitId = button.dataset.unitId;
            document.getElementById('unit_id').value = unitId;
            document.getElementById('transaction_id').value = '';
            document.getElementById('partsList').innerHTML = '';
            document.getElementById('transactionModalLabel').textContent = 'Part Out Unit';

            // Call handleStatusChange to set initial state of WO/PO fields
            handleStatusChange();

            // Fetch unit parts via AJAX
            fetch('/unit-transactions/get-unit-parts/' + unitId)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    // Display unit information
                    const unit = response.unit;
                    const unitInfoHtml = `
                        <div class="mb-3">
                            <strong>Unit Code:</strong> ${unit.unit_code}
                        </div>
                        <div class="mb-3">
                            <strong>Unit Type:</strong> ${unit.unit_type}
                        </div>
                        <div>
                            <strong>Site:</strong> ${unit.site_id}
                        </div>
                    `;
                    document.getElementById('unit-info').innerHTML = unitInfoHtml;
                    document.getElementById('pekerjaan').value = unit.pekerjaan;
                    document.getElementById('HMKM').value = unit.HMKM;
                    document.getElementById('SHIFT').value = unit.SHIFT;
                    document.getElementById('LOKASI').value = unit.LOKASI;

                    // Set contact to the current user's name
                    const userName = document.getElementById('current-user-name').value;
                    document.getElementById('contact').value = userName;

                    // Set customer based on site ID
                    const siteId = document.getElementById('current-site-id').value || '';
                    let customerValue = siteId;
                    if (siteId === 'PPA') {
                        customerValue = 'PT. PUTRA PERKASA ABADI';
                    } else if (siteId === 'UDU') {
                        customerValue = 'PT. Unggul Dinamika Utama';
                    } else if (siteId === 'IMK') {
                        customerValue = 'PT. Indo Muro Kencana';
                    } else if (siteId === 'DH') {
                        customerValue = 'PT. DH SITE AGRAHASAN';
                    }
                    document.getElementById('customer').value = customerValue;

                    // Set the next DO number by extracting and incrementing the numeric part
                    const currentSiteId = document.getElementById('current-site-id')?.value || '';

                    // For DH site, always leave DO number empty
                    if (currentSiteId === 'DH') {
                        document.getElementById('do_number').value = '';
                        document.getElementById('do_number').dataset.originalValue = '';
                    } else {
                        // For other sites, use the original logic
                        if (unit.do_number) {
                            // Use regex to extract the last numeric part
                            const doNumberRegex = /(.*?)(\d+)([^\d]*)$/;
                            const match = unit.do_number.match(doNumberRegex);

                            if (match) {
                                const prefix = match[1]; // Everything before the last numeric part
                                const numericPart = match[2]; // The last numeric part
                                const suffix = match[3]; // Anything after the last numeric part (usually empty)
                                const nextNumericPart = parseInt(numericPart) + 1;

                                // Format the numeric part to have the same number of digits (preserve leading zeros)
                                const digitCount = numericPart.length;
                                const formattedNumericPart = String(nextNumericPart).padStart(digitCount, '0');

                                const nextDoNumber = prefix + formattedNumericPart + suffix;
                                document.getElementById('do_number').value = nextDoNumber;
                                document.getElementById('do_number').dataset.originalValue = unit.do_number;
                            } else {
                                // If no numeric part found, just use the original
                                document.getElementById('do_number').value = unit.do_number;
                                document.getElementById('do_number').dataset.originalValue = unit.do_number;
                            }
                        } else {
                            // Default DO number if none exists
                            document.getElementById('do_number').value = '';
                            document.getElementById('do_number').dataset.originalValue = '';
                        }
                    }

                    // Set the next SPB number by extracting and incrementing the numeric part
                    if (unit.noSPB) {
                        // Use regex to extract the last numeric part
                        const spbNumberRegex = /(.*?)(\d+)([^\d]*)$/;
                        const match = unit.noSPB.match(spbNumberRegex);

                        if (match) {
                            const prefix = match[1]; // Everything before the last numeric part
                            const numericPart = match[2]; // The last numeric part
                            const suffix = match[3]; // Anything after the last numeric part (usually empty)
                            const nextNumericPart = parseInt(numericPart) + 1;

                            // Format the numeric part to have the same number of digits (preserve leading zeros)
                            const digitCount = numericPart.length;
                            const formattedNumericPart = String(nextNumericPart).padStart(digitCount, '0');

                            const nextSpbNumber = prefix + formattedNumericPart + suffix;
                            document.getElementById('noSPB').value = nextSpbNumber;
                            document.getElementById('noSPB').dataset.originalValue = unit.noSPB;
                        } else {
                            // If no numeric part found, just use the original
                            document.getElementById('noSPB').value = unit.noSPB;
                            document.getElementById('noSPB').dataset.originalValue = unit.noSPB;
                        }
                    } else {
                        // Default SPB number if none exists
                        document.getElementById('noSPB').value = '';
                        document.getElementById('noSPB').dataset.originalValue = '';
                    }

                    // Add parts to form
                    response.parts.forEach(part => {
                        addPartToForm(part);
                    });

                    // Show the transaction modal manually
                    closeDetailsModal(); // Close any open details modal first
                    const modalEl = document.getElementById('transactionModal');
                    modalEl.classList.add('show');
                    modalEl.style.display = 'block';
                    document.body.classList.add('modal-open');
                    document.body.style.overflow = 'hidden';

                    // Add backdrop if it doesn't exist
                    if (!document.querySelector('.modal-backdrop')) {
                        const backdrop = document.createElement('div');
                        backdrop.classList.add('modal-backdrop', 'fade', 'show');
                        document.body.appendChild(backdrop);
                    }
                })
                .catch(error => {
                    console.error('Error loading unit parts:', error);
                    showAlert('Kesalahan memuat part unit', 'error');
                });
        }
    });

    // These functions are now defined globally at the top of the file

    // Function to add part to form
    function addPartToForm(part) {
        // Determine part inventory ID based on the structure
        const partInventoryId = part.part_inventory_id || part.part_inventory?.part_inventory_id;

        // Get part name and code
        let partName = '-';
        let partCode = '-';

        if (part.part_inventory && part.part_inventory.part) {
            partName = part.part_inventory.part.part_name;
            partCode = part.part_inventory.part.part_code;
        } else if (part.part) {
            partName = part.part.name || part.part.part_name;
            partCode = part.part.part_number || part.part.part_code;
        }

        // Ensure price is a proper number
        const price = parseFloat(part.price);
        console.log('Part price before formatting:', price);

        const partHtml = `
            <div class="p-2 mb-0 part-item border-bottom" data-part-id="${partInventoryId}">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">${partName}</h5>
                        <small class="text-muted">Code: ${partCode}</small>
                    </div>
                    <div class="col-md-1">
                        <input type="number" class="form-control quantity"
                               value="${part.quantity}" min="0.1" step="0.1" required>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control price"
                               value="${formatRupiah(price)}" required>
                        <input type="hidden" class="price-raw" value="${price}">
                    </div>
                    <div class="col-md-1">
                        <input type="text" class="form-control eum"
                               value="${part.eum}" required>
                    </div>
                    <div class="col-md-1 text-center">
                        <button type="button" class="btn btn-danger btn-sm btn-remove-part">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('partsList').insertAdjacentHTML('beforeend', partHtml);
    }

    // Remove part from list
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-remove-part')) {
            e.target.closest('.part-item').remove();
        }
    });

    // Add event listener for price input formatting
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('price')) {
            // Get the raw value (remove formatting)
            const rawValue = parseRupiah(e.target.value);

            // Update the hidden input with the raw value
            const hiddenInput = e.target.closest('.col-md-3').querySelector('.price-raw');
            if (hiddenInput) {
                hiddenInput.value = rawValue;
            }

            // Format the visible input
            e.target.value = formatRupiah(rawValue);
        }

        // For IMK site, copy MR number (noSPB) to PO number field
        const currentSiteId = document.getElementById('current-site-id')?.value || '';
        if (currentSiteId === 'IMK' && e.target.id === 'noSPB') {
            const poNumberInput = document.getElementById('po_number');
            if (poNumberInput) {
                poNumberInput.value = e.target.value;
            }
        }
    });

    // View transaction details
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-view-transaction')) {
            const button = e.target.closest('.btn-view-transaction');
            const transactionId = button.dataset.id;

            fetch(`/unit-transactions/${transactionId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    renderTransactionDetails(response);

                    // Show the details modal manually
                    closeTransactionModal(); // Close any open transaction modal first
                    const modalEl = document.getElementById('transactionDetailsModal');
                    modalEl.classList.add('show');
                    modalEl.style.display = 'block';
                    document.body.classList.add('modal-open');
                    document.body.style.overflow = 'hidden';

                    // Add backdrop if it doesn't exist
                    if (!document.querySelector('.modal-backdrop')) {
                        const backdrop = document.createElement('div');
                        backdrop.classList.add('modal-backdrop', 'fade', 'show');
                        document.body.appendChild(backdrop);
                    }

                    // Show or hide edit button based on status
                    // Hide edit button for 'Ready PO' and 'Selesai' status
                    if (response.status === 'Ready PO' || response.status === 'Selesai' || response.status === 'selesai') {
                        document.getElementById('edit-transaction-btn').style.display = 'none';
                    } else {
                        document.getElementById('edit-transaction-btn').style.display = 'block';
                        document.getElementById('edit-transaction-btn').dataset.id = response.id;
                    }
                })
                .catch(error => {
                    console.error('Error loading transaction details:', error);
                    showAlert('Kesalahan memuat detail transaksi', 'error');
                });
        }
    });

    // Edit transaction button click
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-edit-transaction') || e.target.closest('#edit-transaction-btn')) {
            const button = e.target.closest('.btn-edit-transaction') || e.target.closest('#edit-transaction-btn');
            const transactionId = button.dataset.id;

            // Hide details modal if it's open
            closeDetailsModal();

            fetch(`/unit-transactions/${transactionId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    resetForm();
                    isEditMode = true;

                    // Set form values
                    document.getElementById('transaction_id').value = response.id;
                    document.getElementById('unit_id').value = response.unit_id;
                    document.getElementById('status').value = response.status;
                    document.getElementById('remarks').value = response.remarks;
                    // We don't set sales_notes in the form as it's for sales staff only
                    document.getElementById('pekerjaan').value = response.pekerjaan;
                    document.getElementById('HMKM').value = response.HMKM;
                    document.getElementById('SHIFT').value = response.SHIFT;
                    document.getElementById('LOKASI').value = response.LOKASI;
                    document.getElementById('contact').value = response.contact || '';
                    document.getElementById('phone').value = response.phone || '';
                    document.getElementById('customer').value = response.customer || '';
                    document.getElementById('sitework').value = response.sitework || '';

                    // Debug: Log the raw date values from server
                    console.log('Raw mr_date from server:', response.mr_date);
                    console.log('Raw do_date from server:', response.do_date);
                    console.log('Type of mr_date:', typeof response.mr_date);
                    console.log('Type of do_date:', typeof response.do_date);

                    // Set MR date if available
                    const formattedMrDate = formatDateForInput(response.mr_date);
                    console.log('Formatted mr_date for input:', formattedMrDate);
                    document.getElementById('mr_date').value = formattedMrDate;

                    // Set DO date if available
                    const formattedDoDate = formatDateForInput(response.do_date);
                    console.log('Formatted do_date for input:', formattedDoDate);
                    document.getElementById('do_date').value = formattedDoDate;

                    // Set DH site-specific fields if we're on DH site
                    if (document.getElementById('current-site-id')?.value === 'DH') {
                        // Set tanggalstart if available
                        document.getElementById('tanggalstart').value = formatDateForInput(response.tanggalstart);

                        // Set tanggalend if available
                        document.getElementById('tanggalend').value = formatDateForInput(response.tanggalend);

                        // Set noireq if available
                        document.getElementById('noireq').value = response.noireq || '';
                    }

                    // Set current attachment value
                    if (response.attachment_path) {
                        document.getElementById('current-attachment').value = response.attachment_path;
                    } else {
                        document.getElementById('current-attachment').value = '';
                    }

                    // Format the updated_at date for the datetime-local input
                    document.getElementById('updated_at').value = formatDateTimeForInput(response.updated_at);

                    // Call handleStatusChange to show/hide WO/PO fields based on status
                    handleStatusChange();

                    // Set WO Number - make sure to get the visible input field
                    const woNumberInput = document.querySelector('input[name="wo_number"].form-control');
                    if (woNumberInput) {
                        woNumberInput.value = response.wo_number || '';
                    }

                    // Set PO Number - make sure to get the visible input field
                    const poNumberInput = document.querySelector('input[name="po_number"].form-control');
                    if (poNumberInput) {
                        poNumberInput.value = response.po_number || '';
                    }

                    // Set Issue Nomor if we're on IMK site
                    if (document.getElementById('current-site-id')?.value === 'IMK') {
                        const issueNomorInput = document.querySelector('input[name="issue_nomor"].form-control');
                        if (issueNomorInput) {
                            issueNomorInput.value = response.issue_nomor || '';
                        }
                    }

                    // Set DO Number
                    const doNumberInput = document.querySelector('input[name="do_number"].form-control');
                    if (doNumberInput) {
                        doNumberInput.value = response.do_number || '';
                        // Store original value for comparison when saving
                        doNumberInput.dataset.originalValue = response.do_number || '';
                    }

                    // Set SPB Number
                    const spbNumberInput = document.querySelector('input[name="noSPB"].form-control');
                    if (spbNumberInput) {
                        spbNumberInput.value = response.noSPB || '';
                        // Store original value for comparison when saving
                        spbNumberInput.dataset.originalValue = response.noSPB || '';
                    }

                    document.getElementById('transactionModalLabel').textContent = 'Edit Transaction';

                    // Display unit information
                    const unit = response.unit;
                    const unitInfoHtml = `
                        <div class="mb-3">
                            <strong>Unit Code:</strong> ${unit.unit_code}
                        </div>
                        <div class="mb-3">
                            <strong>Unit Type:</strong> ${unit.unit_type}
                        </div>
                        <div>
                            <strong>Site:</strong> ${unit.site_id}
                        </div>
                    `;
                    document.getElementById('unit-info').innerHTML = unitInfoHtml;

                    // Add parts to form
                    document.getElementById('partsList').innerHTML = '';
                    response.parts.forEach(part => {
                        addPartToForm(part);
                    });

                    // Show the add part container when in edit mode
                    const addPartContainer = document.getElementById('add-part-container');
                    if (addPartContainer) {
                        addPartContainer.style.display = 'flex';
                    }

                    // Clear the part search input and hide dropdown
                    const partSearchInput = document.getElementById('part-search');
                    const partSearchResults = document.getElementById('part-search-results');
                    if (partSearchInput) {
                        partSearchInput.value = '';
                        currentSearchResults = [];
                    }
                    if (partSearchResults) {
                        partSearchResults.style.display = 'none';
                    }

                    // Show the transaction modal manually
                    const modalEl = document.getElementById('transactionModal');
                    modalEl.classList.add('show');
                    modalEl.style.display = 'block';
                    document.body.classList.add('modal-open');
                    document.body.style.overflow = 'hidden';

                    // Add backdrop if it doesn't exist
                    if (!document.querySelector('.modal-backdrop')) {
                        const backdrop = document.createElement('div');
                        backdrop.classList.add('modal-backdrop', 'fade', 'show');
                        document.body.appendChild(backdrop);
                    }
                })
                .catch(error => {
                    console.error('Error loading transaction for editing:', error);
                    showAlert('Kesalahan memuat transaksi untuk diedit', 'error');
                });
        }
    });

    // Function to render transaction details
    function renderTransactionDetails(transaction) {
        let partsHtml = '';
        transaction.parts.forEach(part => {
            // Get part name and code
            let partName = '-';
            let partCode = '-';

            if (part.part_inventory && part.part_inventory.part) {
                partName = part.part_inventory.part.part_name;
                partCode = part.part_inventory.part.part_code;
            }

            partsHtml += `
                <tr>
                    <td>
                        <h5 class="mb-0">${partName}</h5>
                        <small class="text-muted">Code: ${partCode}</small>
                    </td>
                    <td>${part.quantity}</td>
                    <td class="text-right">${formatRupiah(parseFloat(part.price))}</td>
                    <td>${part.eum}</td>
                    <td class="text-right">${formatRupiah(parseFloat(part.price) * parseFloat(part.quantity))}</td>
                </tr>
            `;
        });
        const totalPrice = transaction.parts.reduce((sum, part) => sum + (parseFloat(part.price) * parseFloat(part.quantity)), 0);
        const detailsHtml = `
            <div class="mb-4">
                <h6 class="font-bold mb-2">Transaction Information</h6>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">ID</th>
                        <td>${transaction.id}</td>
                    </tr>
                    <tr>
                        <th>Unit</th>
                        <td>${transaction.unit.unit_code} - ${transaction.unit.unit_type}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <span class="badge ${getStatusBadgeClass(transaction.status)}">
                                ${transaction.status}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>WO Number</th>
                        <td>${transaction.wo_number || '-'}</td>
                    </tr>
                    <tr>
                        <th>PO Number</th>
                        <td>${transaction.po_number || '-'}</td>
                    </tr>
                    ${document.getElementById('current-site-id')?.value === 'IMK' ? `
                    <tr>
                        <th>Issue Nomor</th>
                        <td>${transaction.issue_nomor || '-'}</td>
                    </tr>` : ''}
                    <tr>
                        <th>MR Date</th>
                        <td>${transaction.mr_date ? formatDate(transaction.mr_date) : '-'}</td>
                    </tr>
                    <tr>
                        <th>DO Number</th>
                        <td>${transaction.do_number || '-'}</td>
                    </tr>
                    <tr>
                        <th>DO Date</th>
                        <td>${transaction.do_date ? formatDate(transaction.do_date) : '-'}</td>
                    </tr>
                    ${document.getElementById('current-site-id')?.value === 'DH' ? `
                    <tr>
                        <th>Tanggal Start</th>
                        <td>${transaction.tanggalstart ? formatDate(transaction.tanggalstart) : '-'}</td>
                    </tr>
                    <tr>
                        <th>Tanggal End</th>
                        <td>${transaction.tanggalend ? formatDate(transaction.tanggalend) : '-'}</td>
                    </tr>
                    <tr>
                        <th>No. IREQ</th>
                        <td>${transaction.noireq || '-'}</td>
                    </tr>` : ''}
                    <tr>
                        <th>Pekerjaan</th>
                        <td>${transaction.pekerjaan || '-'}</td>
                    </tr>
                    <tr>
                        <th>HM / KM</th>
                        <td>${transaction.HMKM || '-'}</td>
                    </tr>
                    <tr>
                        <th>SHIFT</th>
                        <td>${transaction.SHIFT || '-'}</td>
                    </tr>
                    <tr>
                        <th>LOKASI</th>
                        <td>${transaction.LOKASI || '-'}</td>
                    </tr>
                    <tr>
                        <th>Contact Person</th>
                        <td>${transaction.contact || '-'}</td>
                    </tr>
                    <tr>
                        <th>Phone Number</th>
                        <td>${transaction.phone || '-'}</td>
                    </tr>
                    <tr>
                        <th>Customer</th>
                        <td>${transaction.customer || '-'}</td>
                    </tr>
                    <tr>
                        <th>Site Work</th>
                        <td>${transaction.sitework || '-'}</td>
                    </tr>
                    <tr>
                        <th>Remarks</th>
                        <td>${transaction.remarks || '-'}</td>
                    </tr>
                    <tr>
                        <th>Catatan Sales</th>
                        <td>${transaction.sales_notes || '-'}</td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>${formatDate(transaction.created_at)}</td>
                    </tr>
                    <tr>
                        <th>Tanggal Update</th>
                        <td>${formatDate(transaction.updated_at)}</td>
                    </tr>
                    <tr>
                        <th>Lampiran</th>
                        <td>${transaction.attachment_path ? `<a href="/assets/lampiranunits/${transaction.attachment_path}" target="_blank" class="btn btn-sm btn-info"><i class="fas fa-download"></i> Unduh Lampiran</a>` : '-'}</td>
                    </tr>
                </table>
            </div>

            <div>
                <h6 class="font-bold mb-2">Parts List</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="bg-dark text-white">
                            <tr>
                                <th>Part Name</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>EUM</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${partsHtml}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-right">Total</th>
                                <th style="text-align: right;">${formatRupiah(totalPrice)}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        `;

        document.getElementById('transaction-details-content').innerHTML = detailsHtml;
    }

    // This function is now defined globally at the top of the file

    // Function to reset form
    function resetForm() {
        document.getElementById('transactionForm').reset();
        document.getElementById('unit_id').value = '';
        document.getElementById('transaction_id').value = '';
        document.getElementById('partsList').innerHTML = '';
        document.getElementById('unit-info').innerHTML = '';

        // Clear all input fields explicitly
        document.getElementById('wo_number').value = '';
        document.getElementById('po_number').value = '';
        // Clear issue_nomor if we're on IMK site
        if (document.getElementById('current-site-id')?.value === 'IMK') {
            document.getElementById('issue_nomor').value = '';
        }
        document.getElementById('do_number').value = '';
        document.getElementById('noSPB').value = '';
        document.getElementById('pekerjaan').value = '';
        document.getElementById('HMKM').value = '';
        document.getElementById('SHIFT').value = '';
        document.getElementById('LOKASI').value = '';
        document.getElementById('updated_at').value = '';
        document.getElementById('remarks').value = '';
        document.getElementById('current-attachment').value = '';
        document.getElementById('mr_date').value = '';
        document.getElementById('do_date').value = '';

        // Clear DH site-specific fields if they exist
        if (document.getElementById('current-site-id')?.value === 'DH') {
            if (document.getElementById('tanggalstart')) document.getElementById('tanggalstart').value = '';
            if (document.getElementById('tanggalend')) document.getElementById('tanggalend').value = '';
            if (document.getElementById('noireq')) document.getElementById('noireq').value = '';
        }
        // Set contact to the current user's name from the hidden input
        const userName = document.getElementById('current-user-name').value;
        document.getElementById('contact').value = userName;
        document.getElementById('phone').value = '';
        // Set customer based on site ID
        const siteId = document.getElementById('current-site-id').value || '';
        let customerValue = siteId;
        if (siteId === 'PPA') {
            customerValue = 'PT. PUTRA PERKASA ABADI';
        } else if (siteId === 'UDU') {
            customerValue = 'PT. Unggul Dinamika Utama';
        } else if (siteId === 'IMK') {
            customerValue = 'PT. Indo Muro Kencana';
        }
        document.getElementById('customer').value = customerValue;
        document.getElementById('sitework').value = '';

        // Hide the add part container by default
        const addPartContainer = document.getElementById('add-part-container');
        if (addPartContainer) {
            addPartContainer.style.display = 'none';
        }

        // Reinitialize dropify
        if (typeof $ !== 'undefined' && $.fn.dropify) {
            $('.dropify').dropify({
                messages: {
                    default: 'Drag and drop a file here or click',
                    replace: 'Drag and drop or click to replace',
                    remove: 'Remove',
                    error: 'Ooops, something wrong happened.'
                },
                error: {
                    fileSize: 'The file size is too big (5M max).'
                }
            });
        }

        // Hide WO and PO fields
        const woContainer = document.getElementById('wo_number_container');
        const poContainer = document.getElementById('po_number_container');
        if (woContainer) woContainer.style.display = 'none';
        if (poContainer) poContainer.style.display = 'none';
    }

    // Handle form submission
    document.getElementById('transactionForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // For DH site, sync hidden fields with visible fields before submission
        if (document.getElementById('current-site-id')?.value === 'DH') {
            setupDHHiddenFields();
        }

        const parts = [];
        document.querySelectorAll('.part-item').forEach(item => {
            const partId = item.dataset.partId;
            // Use the raw price value from the hidden input
            const priceRaw = parseFloat(item.querySelector('.price-raw').value);
            parts.push({
                part_inventory_id: partId,
                quantity: parseFloat(item.querySelector('.quantity').value),
                price: priceRaw,
                eum: item.querySelector('.eum').value
            });
        });

        if (parts.length === 0) {
            showAlert('Mohon tambahkan minimal satu part ke transaksi', 'error');
            return;
        }

        // Get form values
        const status = document.getElementById('status').value;

        // Check if there's a file to upload
        const attachmentInput = document.getElementById('attachment');
        const hasAttachment = attachmentInput && attachmentInput.files && attachmentInput.files.length > 0;

        // Check file size if there's an attachment
        if (hasAttachment) {
            const fileSize = attachmentInput.files[0].size;
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (fileSize > maxSize) {
                showAlert('Ukuran file terlalu besar. Maksimal 5MB.', 'error');
                return;
            }
        }

        // Use FormData for file uploads
        const formData = new FormData();
        formData.append('unit_id', document.getElementById('unit_id').value);
        formData.append('status', status);
        formData.append('mr_date', document.getElementById('mr_date').value);
        formData.append('wo_number', document.getElementById('wo_number').value);
        formData.append('po_number', document.getElementById('po_number').value);
        // Add issue_nomor if we're on IMK site
        if (document.getElementById('current-site-id')?.value === 'IMK') {
            formData.append('issue_nomor', document.getElementById('issue_nomor').value);
        }
        formData.append('do_number', document.getElementById('do_number').value);
        formData.append('do_date', document.getElementById('do_date').value);

        // Add DH site-specific fields if we're on DH site
        if (document.getElementById('current-site-id')?.value === 'DH') {
            formData.append('tanggalstart', document.getElementById('tanggalstart').value);
            formData.append('tanggalend', document.getElementById('tanggalend').value);
            formData.append('noireq', document.getElementById('noireq').value);
        }

        formData.append('noSPB', document.getElementById('noSPB').value);
        formData.append('remarks', document.getElementById('remarks').value);
        // We don't include sales_notes in the site admin form as it's for sales staff only
        formData.append('pekerjaan', document.getElementById('pekerjaan').value);
        formData.append('HMKM', document.getElementById('HMKM').value);
        formData.append('SHIFT', document.getElementById('SHIFT').value);
        formData.append('LOKASI', document.getElementById('LOKASI').value);
        formData.append('contact', document.getElementById('contact').value);
        formData.append('phone', document.getElementById('phone').value);
        formData.append('customer', document.getElementById('customer').value);
        formData.append('sitework', document.getElementById('sitework').value);
        formData.append('updated_at', document.getElementById('updated_at').value);

        // Add parts as individual array items
        for (let i = 0; i < parts.length; i++) {
            formData.append(`parts[${i}][part_inventory_id]`, parts[i].part_inventory_id);
            formData.append(`parts[${i}][quantity]`, parts[i].quantity);
            formData.append(`parts[${i}][price]`, parts[i].price);
            formData.append(`parts[${i}][eum]`, parts[i].eum);
        }

        // Add file if present
        if (hasAttachment) {
            formData.append('attachment', attachmentInput.files[0]);
        }

        const currentSiteId = document.getElementById('current-site-id')?.value || '';

        // For UDU site, only require No. PR field when status is 'Ready PO'
        if (currentSiteId === 'UDU' && status === 'Ready PO' && !document.getElementById('wo_number').value.trim()) {
            showAlert('No. PR diperlukan untuk status Ready PO di site UDU', 'error');
            return;
        }

        // For Ready PO status, validate that PO number is not empty (except for UDU site)
        if (status === 'Ready PO' && currentSiteId !== 'UDU') {
            if (!document.getElementById('po_number').value.trim()) {
                showAlert('Nomor PO diperlukan untuk status Ready PO', 'error');
                return;
            }
        }

        // For Ready PO status, always require attachment regardless of site
        if (status === 'Ready PO' && isEditMode && !hasAttachment && !document.getElementById('current-attachment').value) {
            showAlert('Lampiran diperlukan untuk status Ready PO', 'error');
            return;
        }

        // For Ready WO status, validate that WO number is not empty (only for non-UDU sites)
        if (currentSiteId !== 'UDU' && status === 'Ready WO' && !document.getElementById('wo_number').value.trim()) {
            showAlert('Nomor WO diperlukan untuk status Ready WO', 'error');
            return;
        }

        const transactionId = document.getElementById('transaction_id').value;
        const url = isEditMode ? `/unit-transactions/${transactionId}` : '/unit-transactions';
        const method = isEditMode ? 'PUT' : 'POST';

        // Add method field for PUT requests since FormData can only use POST
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Check if DO number is being edited
        const doNumberInput = document.querySelector('input[name="do_number"].form-control');
        const originalDoNumber = isEditMode ? doNumberInput.dataset.originalValue || '' : '';
        const doNumberChanged = isEditMode && originalDoNumber !== formData.do_number;

        // Check if SPB number is being edited
        const spbNumberInput = document.querySelector('input[name="noSPB"].form-control');
        const originalSpbNumber = isEditMode ? spbNumberInput.dataset.originalValue || '' : '';
        const spbNumberChanged = isEditMode && originalSpbNumber !== formData.noSPB;

        // Check if status is 'Ready PO' and show confirmation
        if (status === 'Ready PO') {
            const poNumber = document.getElementById('po_number').value;

            Swal.fire({
                title: 'Konfirmasi Nomor PO',
                html: `<div class="text-left">
                        <p>Anda akan menyimpan dengan status <strong>Ready PO</strong>.</p>
                        <p>Mohon pastikan Nomor PO sudah benar:</p>
                        <p class="font-weight-bold text-primary">${poNumber}</p>
                        <p class="text-danger"><strong>Peringatan:</strong> Setelah disimpan, Anda tidak akan dapat mengedit transaksi ini.</p>
                    </div>
                    `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Ya, simpan!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Proceed with saving
                    submitFormData(url, 'POST', formData, csrfToken);
                }
            });
        } else if (doNumberChanged && spbNumberChanged) {
            // Show confirmation for both DO and SPB number changes
            Swal.fire({
                title: 'Konfirmasi Perubahan Nomor',
                html: `<div class="text-left">
                        <p>Anda akan mengubah Nomor DO dan SPB:</p>
                        <p><strong>Nomor DO:</strong></p>
                        <p>Dari: <span class="font-weight-bold text-secondary">${originalDoNumber || 'Tidak ada'}</span></p>
                        <p>Ke: <span class="font-weight-bold text-primary">${formData.do_number}</span></p>
                        <p><strong>Nomor SPB:</strong></p>
                        <p>Dari: <span class="font-weight-bold text-secondary">${originalSpbNumber || 'Tidak ada'}</span></p>
                        <p>Ke: <span class="font-weight-bold text-primary">${formData.noSPB}</span></p>
                        <p class="text-danger"><strong>Peringatan:</strong> Mengubah nomor-nomor ini akan mempengaruhi semua laporan terkait.</p>
                        <p>Pastikan format nomor sudah benar (contoh: PPA-PWB-111, SPB-PWB-111).</p>
                    </div>
                    `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Simpan',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Proceed with saving
                    submitFormData(url, 'POST', formData, csrfToken);
                }
            });
        } else if (doNumberChanged) {
            // Show confirmation for DO number change
            Swal.fire({
                title: 'Konfirmasi Perubahan Nomor DO',
                html: `<div class="text-left">
                        <p>Anda akan mengubah Nomor DO:</p>
                        <p>Dari: <span class="font-weight-bold text-secondary">${originalDoNumber || 'Tidak ada'}</span></p>
                        <p>Ke: <span class="font-weight-bold text-primary">${formData.do_number}</span></p>
                        <p class="text-danger"><strong>Peringatan:</strong> Mengubah Nomor DO akan mempengaruhi semua laporan terkait.</p>
                        <p>Pastikan format Nomor DO sudah benar (contoh: PPA-PWB-111).</p>
                    </div>
                    `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Simpan',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Proceed with saving
                    submitFormData(url, 'POST', formData, csrfToken);
                }
            });
        } else if (spbNumberChanged) {
            // Show confirmation for SPB number change
            Swal.fire({
                title: 'Konfirmasi Perubahan Nomor SPB',
                html: `<div class="text-left">
                        <p>Anda akan mengubah Nomor SPB:</p>
                        <p>Dari: <span class="font-weight-bold text-secondary">${originalSpbNumber || 'Tidak ada'}</span></p>
                        <p>Ke: <span class="font-weight-bold text-primary">${formData.noSPB}</span></p>
                        <p class="text-danger"><strong>Peringatan:</strong> Mengubah Nomor SPB akan mempengaruhi semua laporan terkait.</p>
                        <p>Pastikan format Nomor SPB sudah benar (contoh: SPB-PWB-111).</p>
                    </div>
                    `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Simpan',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Proceed with saving
                    submitFormData(url, 'POST', formData, csrfToken);
                }
            });
        } else {
            // For other statuses, proceed without confirmation
            submitFormData(url, 'POST', formData, csrfToken);
        }
    });

    // Function to submit form data
    function submitFormData(url, method, formData, csrfToken) {
        // Check if formData is a FormData object
        const isFormDataObject = formData instanceof FormData;

        // If it's not a FormData object, we need to handle it differently
        if (!isFormDataObject) {
            const currentSiteId = document.getElementById('current-site-id')?.value || '';

            // For UDU site, only require No. PR field when status is 'Ready PO'
            if (currentSiteId === 'UDU' && formData.status === 'Ready PO' && (!formData.wo_number || (typeof formData.wo_number === 'string' && !formData.wo_number.trim()))) {
                showAlert('No. PR diperlukan untuk status Ready PO di site UDU', 'error');
                return;
            }

            // For Ready PO status, validate that PO number is not empty (except for UDU site)
            if (currentSiteId !== 'UDU' && formData.status === 'Ready PO' && (!formData.po_number || (typeof formData.po_number === 'string' && !formData.po_number.trim()))) {
                showAlert('Nomor PO diperlukan untuk status Ready PO', 'error');
                return;
            }

            // For Ready WO status, validate that WO number is not empty (only for non-UDU sites)
            if (currentSiteId !== 'UDU' && formData.status === 'Ready WO' && (!formData.wo_number || (typeof formData.wo_number === 'string' && !formData.wo_number.trim()))) {
                showAlert('Nomor WO diperlukan untuk status Ready WO', 'error');
                return;
            }

            // Create a new FormData object
            const newFormData = new FormData();

            // Add all properties from the original formData object
            for (const key in formData) {
                if (key === 'parts') {
                    // Add parts as individual array items
                    for (let i = 0; i < formData[key].length; i++) {
                        newFormData.append(`parts[${i}][part_inventory_id]`, formData[key][i].part_inventory_id);
                        newFormData.append(`parts[${i}][quantity]`, formData[key][i].quantity);
                        newFormData.append(`parts[${i}][price]`, formData[key][i].price);
                        newFormData.append(`parts[${i}][eum]`, formData[key][i].eum);
                    }
                } else {
                    newFormData.append(key, formData[key]);
                }
            }

            // Add method field for PUT requests
            if (method === 'PUT') {
                newFormData.append('_method', 'PUT');
            }

            // Add CSRF token
            newFormData.append('_token', csrfToken);

            // Replace the original formData with the new FormData object
            formData = newFormData;
        }

        // For FormData objects, we need to add the _method field for PUT requests
        if (isFormDataObject && method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        // Use fetch API with FormData
        fetch(url, {
            method: 'POST', // Always use POST with FormData, Laravel will handle the method via _method
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json'
                // Don't set Content-Type with FormData, browser will set it with boundary
            },
            body: formData
        })
        .then(response => {
            // First check if the response is OK
            if (!response.ok) {
                // Try to parse as JSON first
                return response.text().then(text => {
                    try {
                        // Try to parse the text as JSON
                        const data = JSON.parse(text);

                        // Check if this is a stock issue error
                        if (data.stock_issues) {
                            // Format stock issues for display
                            let stockIssuesHtml = '<div class="text-left"><ul class="list-group">';
                            data.stock_issues.forEach(issue => {
                                stockIssuesHtml += `
                                    <li class="list-group-item">
                                        <strong>${issue.part_name}</strong> (${issue.part_code})<br>
                                        Tersedia: <span class="text-danger">${issue.available}</span>,
                                        Diminta: <span class="text-primary">${issue.requested}</span>
                                    </li>
                                `;
                            });
                            stockIssuesHtml += '</ul></div>';

                            // Show stock issues in a SweetAlert
                            Swal.fire({
                                title: 'Stok Tidak Mencukupi',
                                html: stockIssuesHtml,
                                icon: 'warning',
                                confirmButtonText: 'OK'
                            });
                            throw new Error('Stok tidak mencukupi untuk satu atau beberapa part');
                        } else {
                            throw new Error(data.error || data.message || 'Terjadi kesalahan');
                        }
                    } catch (e) {
                        // If it's not valid JSON, it's probably an HTML error page
                        console.error('Response is not valid JSON:', text.substring(0, 150) + '...');
                        throw new Error(`Server error: ${response.status} ${response.statusText}`);
                    }
                });
            }

            // If response is OK, parse as JSON
            return response.json();
        })
        .then(data => {
            // Hide the transaction modal manually
            closeTransactionModal();

            // Show success message
            showAlert(isEditMode ? 'Transaksi berhasil diperbarui' : 'Transaksi berhasil dibuat', 'success');

            // Force reload transactions with a slight delay to ensure server has processed the changes
            setTimeout(function() {
                window.loadTransactions(currentPage);
            }, 300);
        })
        .catch(error => {
            console.error('Error:', error);
            if (error.message !== 'Stok tidak mencukupi untuk satu atau beberapa part') {
                showAlert(error.message || 'Terjadi kesalahan', 'error');
            }
        });
    }
});
// This function is now defined globally at the top of the file

// Add event listener for page visibility changes to hide loading overlay
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Hide loading overlay when page becomes visible again
        const overlay = document.getElementById('file-loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
});