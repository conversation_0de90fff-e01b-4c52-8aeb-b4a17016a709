<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="utf-8" />
    <title>@yield('title', 'Portal PWB - Kasir Dashboard')</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- CSS Files -->
    @vite('resources/assets/css/bootstrap.min.css')
    @vite('resources/assets/css/icons.min.css')
    @vite('resources/assets/css/app.min.css')
    @vite('resources/css/app.css')
    @vite('resources/css/kasir-dashboard.css')

    <style>
        :root {
            --primary-color: #225297;
            --secondary-color: #58c0f6;
            --accent-color: #1e4785;
            --accent-hover-color: #3a6db5;
            --highlight-color: #e8f4ff;
            --danger-color: #eb3124;
            --success-color: #97f784;
            --info-color: #58c0f6;
            --warning-color: #feff8c;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        /* Global styles for kasir pages */
        body.kasir-body {
            font-size: 11px;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-family: 'Nunito', sans-serif;
        }

        .kasir-body .btn {
            font-size: 11px;
        }

        .kasir-body .table {
            font-size: 11px;
        }

        .kasir-body .form-control,
        .kasir-body .form-select {
            font-size: 11px;
        }

        .kasir-body .modal-body {
            font-size: 11px;
        }

        .kasir-body .card-body {
            font-size: 11px;
        }

        .kasir-body .dropdown-menu {
            font-size: 11px;
        }

        .kasir-body .badge {
            font-size: 11px;
        }

        .kasir-body .pagination {
            font-size: 11px;
        }

        /* Kasir-specific styles */
        .kasir-body .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            border: none;
        }

        .kasir-body .card-header {
            background-color: #225297;
            color: white;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .kasir-body .btn-primary {
            background-color: #225297;
            border-color: #225297;
        }

        .kasir-body .btn-primary:hover {
            background-color: #1e4785;
            border-color: #1e4785;
        }

        .kasir-body .btn-danger {
            background-color: #eb3124;
            border-color: #eb3124;
        }

        .kasir-body .btn-success {
            background-color: #97f784;
            border-color: #97f784;
            color: #343a40;
        }

        .kasir-body .btn-warning {
            background-color: #feff8c;
            border-color: #feff8c;
            color: #343a40;
        }

        .kasir-body .table thead th {
            background-color: #f8f9fa;
            color: #343a40;
        }

        .kasir-body .pagination .page-item .page-link {
            color: #225297;
        }

        .kasir-body .pagination .page-item.active .page-link {
            background-color: #225297;
            border-color: #225297;
            color: white;
        }

        /* Header styles */
        .kasir-header {
            background: linear-gradient(135deg, #225297 0%, #58c0f6 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .kasir-header .navbar-brand {
            font-weight: bold;
            font-size: 1.2rem;
        }

        .kasir-header .btn-outline-light {
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }

        .kasir-header .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: white;
        }

        /* Skeleton loader */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .skeleton-text {
            height: 1rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .skeleton-card {
            height: 100px;
            border-radius: 8px;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state img {
            max-width: 200px;
            opacity: 0.7;
            margin-bottom: 1rem;
        }

        /* Currency input styling */
        .currency-input {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .kasir-body {
                font-size: 10px;
            }

            .kasir-body .btn {
                font-size: 10px;
                padding: 0.25rem 0.5rem;
            }
        }
    </style>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>

<body class="kasir-body">
    <!-- Header -->
    <header class="kasir-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo" height="40" class="me-3">
                    <span class="navbar-brand mb-0">Portal PWB - Kasir</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-3">{{ Auth::user()->name }}</span>
                    <span class="me-3 text-light">|</span>
                    <span class="me-3">{{ now()->setTimezone('Asia/Makassar')->format('d-m-Y H:i') }}</span>
                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-outline-light btn-sm">
                            <i class="mdi mdi-logout"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div id="wrapper">
        <div class="content-page p-0 m-0">
            <div class="content">
                <div class="container-fluid p-3">
                    @yield('contentkasir')
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    @vite("resources/assets/js/vendor.min.js")
    @vite("resources/assets/js/app.min.js")
    @vite("resources/js/utils/dateUtils.js")
    @yield('resourcekasir')
    @yield('scripts')
</body>

</html>
