<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <title>PWB LOGIN</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/login.css', 'resources/js/login/login.js', 'resources/js/login/superadmin-login.js']); ?>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: url('<?php echo e(asset('assets/images/438463.png')); ?>');
            background-size: cover;
            background-position: center;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div class="background-effects">
        <!-- Partikel latar belakang -->
        <div class="particle" style="left: 10%; width: 80px; height: 80px; animation-delay: 0s"></div>
        <div class="particle" style="left: 30%; width: 120px; height: 120px; animation-delay: -5s"></div>
        <div class="particle" style="left: 70%; width: 100px; height: 100px; animation-delay: -10s"></div>
    </div>

    <div class="container">
        <div class="login-section">
            <div class="login-box">
                <h2>LOGIN</h2>
                <?php if($errors->any()): ?>
                        <div class="alert alert-danger" style="color: red; margin-top: 10px; background-color: #f8fbb7; margin-bottom: 10px; padding: 5px;">
                            <?php echo e($errors->first()); ?>

                        </div>
                    <?php endif; ?>
                <form id="login-form" action="<?php echo e(route('login')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" name="username" id="username-input" placeholder="Username" required value="">
                    </div>
                    <div class="input-group" id="password-input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" name="password" id="password-input" placeholder="Password atau Kode" required value="">
                    </div>
                    <button type="submit">Masuk</button>
                </form>
                <div class="footer" style="margin-top: 15px; text-align: center;">
                    <a href="<?php echo e(route('token.login.form')); ?>" style="color: #4a90e2; text-decoration: none; font-size: 14px;">
                        <i class="fas fa-key"></i> Login dengan Token
                    </a>
                </div>
            </div>
        </div>
        <div class="chart-section">
            <img src="<?php echo e(asset('assets/images/6333220.png')); ?>" alt="Background Graphic">
        </div>
    </div>
</body>
</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/auth/login.blade.php ENDPATH**/ ?>