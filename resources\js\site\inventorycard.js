document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let searchTerm = '';
    let partType = '';
    let statusFilter = '';
    let sortBy = 'part_code';
    let sortDirection = 'asc';

    // Get the current site ID from the hidden input
    const currentSiteId = document.getElementById('current-site-id').value;

    // Initial load
    loadInventoryData();

    // Initialize sort icons
    updateSortIcons();

    // Search button click event
    document.getElementById('search-button').addEventListener('click', function() {
        searchTerm = document.getElementById('search-input').value;
        currentPage = 1;
        loadInventoryData();
    });

    // Search input enter key event
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchTerm = document.getElementById('search-input').value;
            currentPage = 1;
            loadInventoryData();
        }
    });

    // Part type filter change event
    document.getElementById('part-type-filter').addEventListener('change', function() {
        partType = this.value;
        currentPage = 1;
        loadInventoryData();
    });

    // Status filter change event
    document.getElementById('status-filter').addEventListener('change', function() {
        statusFilter = this.value;
        currentPage = 1;
        loadInventoryData();
    });

    // Sorting event listeners
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const newSortBy = this.getAttribute('data-sort');

            // Toggle sort direction if clicking the same column
            if (sortBy === newSortBy) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortBy = newSortBy;
                sortDirection = 'asc';
            }

            // Update sort icons
            updateSortIcons();

            currentPage = 1;
            loadInventoryData();
        });
    });

    function updateSortIcons() {
        // Reset all sort icons
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'mdi mdi-sort sort-icon';
        });

        // Set active sort icon
        const activeHeader = document.querySelector(`[data-sort="${sortBy}"]`);
        if (activeHeader) {
            const icon = activeHeader.querySelector('.sort-icon');
            if (sortDirection === 'asc') {
                icon.className = 'mdi mdi-sort-ascending sort-icon';
            } else {
                icon.className = 'mdi mdi-sort-descending sort-icon';
            }
        }
    }

    function loadInventoryData() {
        // Show loading state
        document.getElementById('inventory-table-body').innerHTML = `
            <tr>
                <td colspan="${currentSiteId === 'IMK' ? 8 : 7}" class="text-center">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Prepare request parameters
        const params = new URLSearchParams({
            page: currentPage,
            per_page: 15,
            search: searchTerm,
            part_type: partType,
            status_filter: statusFilter,
            sort_by: sortBy,
            sort_direction: sortDirection
        });

        // Fetch data from server
        fetch(`/inventory-card/data?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            renderInventoryTable(data);
            renderPagination(data);
        })
        .catch(error => {
            console.error('Error fetching inventory data:', error);
            document.getElementById('inventory-table-body').innerHTML = `
                <tr>
                    <td colspan="${currentSiteId === 'IMK' ? 8 : 7}" class="text-center text-danger">
                        Error loading data. Please try again.
                    </td>
                </tr>
            `;
        });
    }

    function renderInventoryTable(data) {
        const tableBody = document.getElementById('inventory-table-body');

        if (data.data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="${currentSiteId === 'IMK' ? 8 : 7}" class="text-center">No data found</td>
                </tr>
            `;
            return;
        }

        let html = '';
        data.data.forEach(item => {
            let statusClass = '';
            if (item.status === 'Not Ready') {
                statusClass = 'text-danger fw-bold';
            } else if (item.status === 'Ready') {
                statusClass = 'text-success fw-bold';
            } else if (item.status === 'Medium') {
                statusClass = 'text-warning fw-bold';
            } else if (item.status === 'Lainnya') {
                statusClass = 'text-muted fw-bold';
            }

            // Format price with thousand separator
            const formattedPrice = new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(item.price || 0);

            html += `
                <tr>
                    <td>${item.part_code}</td>
                    ${currentSiteId === 'IMK' ? `<td>${item.itemcode || '-'}</td>` : ''}
                    <td>${item.part_name}</td>
                    <td>${parseFloat(item.stock_quantity).toFixed(1)}</td>
                    <td>${item.min_stock}</td>
                    <td>${item.max_stock}</td>
                    <td>${formattedPrice}</td>
                    <td class="${statusClass}">${item.status}</td>
                </tr>
            `;
        });

        tableBody.innerHTML = html;

        // Update pagination info
        document.getElementById('pagination-info').textContent =
            `Showing ${(data.current_page - 1) * data.per_page + 1} to ${Math.min(data.current_page * data.per_page, data.total)} of ${data.total} entries`;
    }

    function renderPagination(data) {
        const paginationContainer = document.getElementById('inventory-pagination');
        const totalPages = data.last_page;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let html = '<ul class="pagination">';

        // Previous button
        html += `
            <li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${data.current_page - 1}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(1, data.current_page - 2);
        const endPage = Math.min(totalPages, data.current_page + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><a class="page-link" href="#">...</a></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === data.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<li class="page-item disabled"><a class="page-link" href="#">...</a></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`;
        }

        // Next button
        html += `
            <li class="page-item ${data.current_page === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${data.current_page + 1}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;

        html += '</ul>';
        paginationContainer.innerHTML = html;

        // Add event listeners to pagination links
        document.querySelectorAll('#inventory-pagination .page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                if (page && page !== currentPage) {
                    currentPage = page;
                    loadInventoryData();
                    document.querySelector('.card').scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    }
});
