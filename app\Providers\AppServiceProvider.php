<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set the default timezone for the application
        date_default_timezone_set(config('app.timezone'));

        // Set Carbon locale to Indonesian for date formatting
        \Carbon\Carbon::setLocale('id');

        $this->app['router']->aliasMiddleware('auth', \App\Http\Middleware\AuthCheck::class);
        $this->app['router']->aliasMiddleware('role', \App\Http\Middleware\RoleMiddleware::class);

        // Register our middleware to ensure log descriptions are never empty
        $this->app['router']->pushMiddlewareToGroup('web', \App\Http\Middleware\EnsureLogDescriptions::class);
    }
}
