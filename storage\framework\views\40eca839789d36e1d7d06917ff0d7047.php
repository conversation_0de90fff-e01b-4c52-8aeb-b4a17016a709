<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/style.js'); ?>
</head>
<body>
<div class="container bgwhite shadow-kit p-4">
    <h1>Add User</h1>
    <form id="createUserForm">
        <div class="form-group">
            <label for="employee_id">NIK</label>
            <input type="text" class="form-control" id="employee_id" name="employee_id" placeholder="Employee ID" autocomplete="off" required>
        </div>
        <div class="form-group">
            <label for="site_id">Site yang dikelola</label>
            <select class="form-control" name="site_id" id="site_id">
                <option value="">-- Pilih Site --</option>
                <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($site->site_id); ?>"><?php echo e($site->site_name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        <div class="form-group">
            <label for="name">Masukkan Nama</label>
            <input type="text" class="form-control" id="name" name="name" placeholder="Name" autocomplete="off" required>
        </div>
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" class="form-control" id="username" name="username" placeholder="Username" autocomplete="off" required>
        </div>
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" class="form-control" id="email" name="email" placeholder="Email">
        </div>
        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" class="form-control" id="password" name="password" placeholder="Password" autocomplete="off" required>
        </div>
        <div class="form-group">
            <label for="role">Role</label>
            <select class="form-control" id="role" name="role">
                <!-- <option value="superadmin">Superadmin</option> -->
                <option value="adminho">AdminHO</option>
                <option value="adminsite">AdminSite</option>
                <!-- <option value="karyawan">Karyawan</option> -->
                <option value="sales">Sales</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Tambahkan</button>
    </form>

    <hr>

    <h2>Current Users</h2>
    <table id="users-table" class="table table-bordered">
        <thead>
            <tr>
                <th>Employee ID</th>
                <th>Site ID</th>
                <th>Name</th>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="users-table-body">
            <!-- User data will be loaded here -->
        </tbody>
    </table>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const createUserForm = document.getElementById('createUserForm');
        const usersTableBody = document.getElementById('users-table-body');

        createUserForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(createUserForm);
            // Convert FormData to a plain object
            const formDataObject = {};
            formData.forEach((value, key) => {
                formDataObject[key] = value;
            });

            // Perform the POST request (replace with your actual endpoint)
            fetch("<?php echo e(route('users.store')); ?>", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>' //If csrf needed
                    },
                    body: JSON.stringify(formDataObject),
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => {
                            throw err;
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    createUserForm.reset();
                    loadUsers();
                    alert(data.success);
                })
                .catch(error => {
                    alert(error.message || "An error occurred");

                });
        });

        function loadUsers() {
            fetch("<?php echo e(route('users.index')); ?>")
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    usersTableBody.innerHTML = ''; // Clear existing rows
                    data.forEach(user => {
                        console.log(data);
                        
                        const row = document.createElement('tr');
                        row.innerHTML = `
                        <td>${user.employee_id}</td>
                        <td>${user.site.site_name || ''}</td>
                        <td>${user.name}</td>
                        <td>${user.username}</td>
                        <td>${user.email || ''}</td>
                        <td>${user.role}</td>
                        <td>
                            <button class="btn btn-primary btn-sm edit-user" data-id="${user.employee_id}">Edit</button>
                            <button class="btn btn-danger btn-sm delete-user" data-id="${user.employee_id}">Delete</button>
                        </td>
                    `;
                        usersTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error("Error loading users:", error);
                });
        }

        // Attach event listeners for dynamically created buttons
        usersTableBody.addEventListener('click', function(e) {
            if (e.target.classList.contains('edit-user')) {
                const userId = e.target.dataset.id;
                // Implement edit logic here
                console.log('Edit user with ID:', userId);
            } else if (e.target.classList.contains('delete-user')) {
                const userId = e.target.dataset.id;
                // Implement delete logic here
                console.log('Delete user with ID:', userId);
            }
        });


        // Optionally load users on page load
        loadUsers();
    });
</script>
    
</body>
</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/users/create.blade.php ENDPATH**/ ?>