// Kasir Dashboard JavaScript
$(document).ready(function() {
    let currentPage = 1;
    let isLoading = false;

    // Initialize
    loadTransactions();
    setupEventListeners();

    function setupEventListeners() {
        // Form submission
        $('#transaction-form').on('submit', handleFormSubmit);
        
        // Filter events
        $('#filter-btn').on('click', handleFilterChange);
        $('#type-filter').on('change', handleFilterChange);
        $('#search-input').on('input', debounce(handleFilterChange, 500));
        
        // Toggle form visibility
        $('#toggle-form-btn').on('click', toggleFormVisibility);
        
        // Currency formatting
        $('#amount').on('input', formatCurrency);
        
        // Attachment preview
        $(document).on('click', '.preview-attachment', handleAttachmentPreview);
    }

    function handleFormSubmit(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const formData = new FormData(this);
        const submitBtn = $('#submit-btn');
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Convert formatted currency back to number
        const amountInput = $('#amount');
        const rawAmount = amountInput.val().replace(/[^\d]/g, '');
        formData.set('amount', rawAmount);
        
        isLoading = true;
        submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>Menyimpan...');
        
        $.ajax({
            url: '/kasir/transactions',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Reset form
                    $('#transaction-form')[0].reset();
                    $('#transaction_date').val(getCurrentDateTime());
                    
                    // Reload data
                    loadTransactions();
                    updateSummary();
                }
            },
            error: function(xhr) {
                let errorMessage = 'Terjadi kesalahan saat menyimpan transaksi';
                
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join('\n');
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: errorMessage
                });
            },
            complete: function() {
                isLoading = false;
                submitBtn.prop('disabled', false).html('<i class="mdi mdi-content-save"></i> Simpan Transaksi');
            }
        });
    }

    function validateForm() {
        const type = $('#type').val();
        const description = $('#description').val().trim();
        const amount = $('#amount').val().replace(/[^\d]/g, '');
        const transactionDate = $('#transaction_date').val();
        
        if (!type) {
            showValidationError('Jenis transaksi harus dipilih');
            return false;
        }
        
        if (!description) {
            showValidationError('Deskripsi transaksi harus diisi');
            return false;
        }
        
        if (!amount || parseInt(amount) <= 0) {
            showValidationError('Jumlah harus diisi dan lebih dari 0');
            return false;
        }
        
        if (!transactionDate) {
            showValidationError('Tanggal transaksi harus diisi');
            return false;
        }
        
        return true;
    }

    function showValidationError(message) {
        Swal.fire({
            icon: 'warning',
            title: 'Perhatian!',
            text: message
        });
    }

    function loadTransactions() {
        if (isLoading) return;
        
        isLoading = true;
        showSkeleton();
        
        const params = {
            page: currentPage,
            type: $('#type-filter').val(),
            search: $('#search-input').val(),
            start_date: $('#start-date').val(),
            end_date: $('#end-date').val()
        };
        
        $.ajax({
            url: '/kasir/transactions/data',
            method: 'GET',
            data: params,
            success: function(response) {
                if (response.success) {
                    renderTransactions(response.data);
                    renderPagination(response.pagination);
                    updateEntriesInfo(response.pagination);
                }
            },
            error: function() {
                showError('Gagal memuat data transaksi');
            },
            complete: function() {
                isLoading = false;
                hideSkeleton();
            }
        });
    }

    function renderTransactions(transactions) {
        const tbody = $('#transactions-table-body');
        tbody.empty();
        
        if (transactions.length === 0) {
            $('#empty-state').removeClass('d-none');
            $('#transactions-container').addClass('d-none');
            return;
        }
        
        $('#empty-state').addClass('d-none');
        $('#transactions-container').removeClass('d-none');
        
        transactions.forEach((transaction, index) => {
            const row = createTransactionRow(transaction, index + 1);
            tbody.append(row);
        });
    }

    function createTransactionRow(transaction, index) {
        const typeClass = transaction.type === 'pengeluaran' ? 'text-danger' : 'text-success';
        const typeIcon = transaction.type === 'pengeluaran' ? 'mdi-arrow-up-bold' : 'mdi-arrow-down-bold';
        const formattedAmount = formatRupiah(transaction.amount);
        const formattedDate = formatDate(transaction.transaction_date);
        
        let attachmentCell = '<span class="text-muted">-</span>';
        if (transaction.attachment) {
            attachmentCell = `<button type="button" class="btn btn-sm btn-outline-primary preview-attachment" data-url="${transaction.attachment_url}">
                <i class="mdi mdi-file"></i> Lihat
            </button>`;
        }
        
        return `
            <tr>
                <td>${index}</td>
                <td>
                    <span class="badge bg-light ${typeClass}">
                        <i class="mdi ${typeIcon}"></i> ${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                    </span>
                </td>
                <td>${transaction.description}</td>
                <td class="text-end">${formattedAmount}</td>
                <td>${formattedDate}</td>
                <td class="text-center">${attachmentCell}</td>
                <td class="text-center">
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewTransactionDetail(${transaction.id})">
                        <i class="mdi mdi-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    function handleFilterChange() {
        currentPage = 1;
        loadTransactions();
        updateSummary();
    }

    function updateSummary() {
        const params = {
            start_date: $('#start-date').val(),
            end_date: $('#end-date').val()
        };
        
        $.ajax({
            url: '/kasir/summary',
            method: 'GET',
            data: params,
            success: function(response) {
                if (response.success) {
                    $('#total-pengeluaran').text(response.data.formatted_pengeluaran);
                    $('#total-penerimaan').text(response.data.formatted_penerimaan);
                }
            }
        });
    }

    function formatCurrency() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value) {
            value = parseInt(value).toLocaleString('id-ID');
        }
        $(this).val(value);
    }

    function formatRupiah(amount) {
        return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function getCurrentDateTime() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    function toggleFormVisibility() {
        const container = $('#transaction-form-container');
        const btn = $('#toggle-form-btn');
        
        if (container.is(':visible')) {
            container.slideUp();
            btn.html('<i class="mdi mdi-eye"></i>');
        } else {
            container.slideDown();
            btn.html('<i class="mdi mdi-eye-off"></i>');
        }
    }

    function handleAttachmentPreview() {
        const url = $(this).data('url');
        const modal = $('#attachment-modal');
        const preview = $('#attachment-preview');
        const downloadBtn = $('#download-attachment');
        
        // Set download link
        downloadBtn.attr('href', url);
        
        // Show preview based on file type
        const extension = url.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png'].includes(extension)) {
            preview.html(`<img src="${url}" class="img-fluid" alt="Preview">`);
        } else if (extension === 'pdf') {
            preview.html(`<embed src="${url}" type="application/pdf" width="100%" height="500px">`);
        } else {
            preview.html(`<p class="text-muted">Preview tidak tersedia untuk file ini. Silakan download untuk melihat.</p>`);
        }
        
        modal.modal('show');
    }

    function showSkeleton() {
        $('#skeleton-loader').removeClass('d-none');
        $('#transactions-container').addClass('d-none');
    }

    function hideSkeleton() {
        $('#skeleton-loader').addClass('d-none');
        $('#transactions-container').removeClass('d-none');
    }

    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: message
        });
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function renderPagination(pagination) {
        // Implementation for pagination rendering
        // This would create pagination buttons based on pagination data
    }

    function updateEntriesInfo(pagination) {
        const info = `Menampilkan ${pagination.from || 0} - ${pagination.to || 0} dari ${pagination.total} transaksi`;
        $('#entries-info').text(info);
    }

    // Global function for viewing transaction details
    window.viewTransactionDetail = function(id) {
        // Implementation for viewing transaction details
        console.log('View transaction detail:', id);
    };
});
