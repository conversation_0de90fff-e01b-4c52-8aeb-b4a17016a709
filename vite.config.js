import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                 // CSS files
                 "resources/css/dashboard.css",
                 "resources/css/login.css",
                 "resources/css/app.css",
                 "resources/css/superadmin-parts.css",
                 "resources/assets/css/bootstrap.min.css",
                 "resources/assets/css/icons.min.css",
                 "resources/assets/css/app.min.css",

                 // Utility JS files
                 "resources/assets/js/app.min.js",
                 "resources/js/utils/slimscroll-config.js",
                 "resources/js/utils/counterup-config.js",
                 "resources/js/utils/tableSearch.js",
                 "resources/js/utils/dateUtils.js",
                 "resources/css/token-login.css",
                 "resources/css/superadmin-dashboard.css",
                 "resources/css/attachment-display.css",
                 "resources/css/superadmin-scaling.css",

                 // Asset JS files
                 "resources/assets/js/vendor.min.js",
                 "resources/assets/js/jquery.min.js",
                 "resources/assets/js/pages/dashboard.init.js",

                 // Core JS files
                 "resources/js/bootstrap-dropdown-fix.js",
                 "resources/js/compatibility.js",
                 "resources/js/bootstrap-init.js",
                 "resources/js/app.js",
                 "resources/js/content.js",
                 "resources/js/style.js",
                 "resources/js/Alert.js",
                 "resources/js/notifikasi.js",
                 "resources/js/chart.js",
                 "resources/js/piechart.js",
                 "resources/js/laporan.js",
                 "resources/js/equipment.js",
                 "resources/js/part_merge.js",

                 // Login related
                 "resources/js/login/login.js",
                 "resources/js/login/superadmin-login.js",

                 // Site related
                  "resources/js/site/notifications.js", // Moved to the top of this section
                 "resources/js/site/Crudsite.js",
                 "resources/js/site/Chartsite.js",
                 "resources/js/site/Dashboard.js",
                 "resources/js/site/Equipmentsite.js",
                 "resources/js/site/Instocksite.js",
                 "resources/js/site/Outstocksite.js",
                 "resources/js/site/Partprioritassite.js",
                 "resources/js/sites/jasa_karyawan.js",
                 "resources/js/sites/jasa_karyawan_hrd.js",
                 "resources/js/sites/jasa_karyawan_sales.js",
                 "resources/js/site/inventorycard.js",

                 // Units related
                 "resources/js/units/UnitTransactions.js",
                 "resources/js/site/Transactionhistory.js",
                 "resources/js/site/Withrawals.js",

                 // Admin HO related
                 "resources/js/adminho/crudsupplier.js",
                 "resources/js/adminho/crudperlengkapan.js",
                 "resources/js/adminho/instockwho.js",
                 "resources/js/adminho/outstock.js",
                 "resources/js/adminho/prioritas_crudpart.js",
                 "resources/js/adminho/public_adminho.js",
                 "resources/js/adminho/stockmonitoring.js",
                 "resources/js/adminho/Transactionhistory.js",
                 "resources/js/adminho/transactions_part.js",
                 "resources/js/adminho/withrawals.js",
                 "resources/js/warehouse/jasa_karyawan.js",

                 // Part related
                 "resources/js/part/Analisis.js",
                 "resources/js/part/Crudpart.js",
                 "resources/js/part/Partgroup.js",

                 // Pengajuan related
                 "resources/js/pengajuan/Confirmationho.js",
                 "resources/js/pengajuan/Pengajuansite.js",

                 // Log aktivitas related
                 "resources/js/site/Logsite.js",
                 "resources/js/adminho/Log.js",

                 // Unit related
                 "resources/js/units.js",
                 "resources/js/units-combined.js",
                 //SALES
                 "resources/js/sales/sales-common.js",
                 "resources/js/sales/dashboard.js",
                 "resources/js/sales/jasa_karyawan.js",
                 "resources/js/sales/penawaran.js",
                 "resources/js/sales/completed-invoices.js",
                 "resources/js/sales/invoices.js",
                 "resources/js/sales/part-list.js",
                 "resources/js/sales/customer.js",

                  //SUPERADMIN
                 "resources/js/superadmin-dashboard-chart.js",
                 "resources/js/superadmin-dashboard-details.js",
                 "resources/js/superadmin-dashboard-scroller.js",
                 "resources/js/superadmin-dashboard-units-modal.js",
                 "resources/js/superadmin-dashboard-best-parts.js",
                 "resources/js/superadmin-dashboard-part-type-modal.js",
                 "resources/js/superadmin-dashboard-jasa-karyawan-modal.js",
                 "resources/js/superadmin-dashboard-sites-data.js",
                 "resources/js/superadmin-parts.js",
                 "resources/js/superadmin-log.js",
                 "resources/js/superadmin-price-list.js",
                 "resources/js/superadmin-invoices.js",
                 "resources/js/superadmin-scaling.js",
                 "resources/js/superadmin-mobile-menu.js",
            ],
            refresh: true,
        }),
    ],
});



